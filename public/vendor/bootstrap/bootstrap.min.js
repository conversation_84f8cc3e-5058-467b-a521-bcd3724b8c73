function Dr(i){if(Object.prototype.hasOwnProperty.call(i,"__esModule"))return i;var n=i.default;if(typeof n=="function"){var o=function l(){var a=!1;try{a=this instanceof l}catch{}return a?Reflect.construct(n,arguments,this.constructor):n.apply(this,arguments)};o.prototype=n.prototype}else o={};return Object.defineProperty(o,"__esModule",{value:!0}),Object.keys(i).forEach(function(l){var a=Object.getOwnPropertyDescriptor(i,l);Object.defineProperty(o,l,a.get?a:{enumerable:!0,get:function(){return i[l]}})}),o}var Dt={exports:{}},W="top",Q="bottom",G="right",z="left",Nt="auto",tt=[W,Q,G,z],Fe="start",Qe="end",dn="clippingParents",si="viewport",Ue="popper",fn="reference",ti=tt.reduce(function(i,n){return i.concat([n+"-"+Fe,n+"-"+Qe])},[]),ri=[].concat(tt,[Nt]).reduce(function(i,n){return i.concat([n,n+"-"+Fe,n+"-"+Qe])},[]),pn="beforeRead",gn="read",mn="afterRead",_n="beforeMain",vn="main",bn="afterMain",yn="beforeWrite",wn="write",An="afterWrite",En=[pn,gn,mn,_n,vn,bn,yn,wn,An];function ue(i){return i?(i.nodeName||"").toLowerCase():null}function Z(i){if(i==null)return window;if(i.toString()!=="[object Window]"){var n=i.ownerDocument;return n&&n.defaultView||window}return i}function Be(i){var n=Z(i).Element;return i instanceof n||i instanceof Element}function ee(i){var n=Z(i).HTMLElement;return i instanceof n||i instanceof HTMLElement}function oi(i){if(typeof ShadowRoot>"u")return!1;var n=Z(i).ShadowRoot;return i instanceof n||i instanceof ShadowRoot}function Pr(i){var n=i.state;Object.keys(n.elements).forEach(function(o){var l=n.styles[o]||{},a=n.attributes[o]||{},h=n.elements[o];!ee(h)||!ue(h)||(Object.assign(h.style,l),Object.keys(a).forEach(function(m){var d=a[m];d===!1?h.removeAttribute(m):h.setAttribute(m,d===!0?"":d)}))})}function Ir(i){var n=i.state,o={popper:{position:n.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(n.elements.popper.style,o.popper),n.styles=o,n.elements.arrow&&Object.assign(n.elements.arrow.style,o.arrow),function(){Object.keys(n.elements).forEach(function(l){var a=n.elements[l],h=n.attributes[l]||{},m=Object.keys(n.styles.hasOwnProperty(l)?n.styles[l]:o[l]),d=m.reduce(function(f,b){return f[b]="",f},{});!ee(a)||!ue(a)||(Object.assign(a.style,d),Object.keys(h).forEach(function(f){a.removeAttribute(f)}))})}}const ai={name:"applyStyles",enabled:!0,phase:"write",fn:Pr,effect:Ir,requires:["computeStyles"]};function ce(i){return i.split("-")[0]}var Me=Math.max,It=Math.min,Ge=Math.round;function ii(){var i=navigator.userAgentData;return i!=null&&i.brands&&Array.isArray(i.brands)?i.brands.map(function(n){return n.brand+"/"+n.version}).join(" "):navigator.userAgent}function Cn(){return!/^((?!chrome|android).)*safari/i.test(ii())}function Ze(i,n,o){n===void 0&&(n=!1),o===void 0&&(o=!1);var l=i.getBoundingClientRect(),a=1,h=1;n&&ee(i)&&(a=i.offsetWidth>0&&Ge(l.width)/i.offsetWidth||1,h=i.offsetHeight>0&&Ge(l.height)/i.offsetHeight||1);var m=Be(i)?Z(i):window,d=m.visualViewport,f=!Cn()&&o,b=(l.left+(f&&d?d.offsetLeft:0))/a,g=(l.top+(f&&d?d.offsetTop:0))/h,E=l.width/a,k=l.height/h;return{width:E,height:k,top:g,right:b+E,bottom:g+k,left:b,x:b,y:g}}function li(i){var n=Ze(i),o=i.offsetWidth,l=i.offsetHeight;return Math.abs(n.width-o)<=1&&(o=n.width),Math.abs(n.height-l)<=1&&(l=n.height),{x:i.offsetLeft,y:i.offsetTop,width:o,height:l}}function On(i,n){var o=n.getRootNode&&n.getRootNode();if(i.contains(n))return!0;if(o&&oi(o)){var l=n;do{if(l&&i.isSameNode(l))return!0;l=l.parentNode||l.host}while(l)}return!1}function ve(i){return Z(i).getComputedStyle(i)}function Nr(i){return["table","td","th"].indexOf(ue(i))>=0}function Ce(i){return((Be(i)?i.ownerDocument:i.document)||window.document).documentElement}function jt(i){return ue(i)==="html"?i:i.assignedSlot||i.parentNode||(oi(i)?i.host:null)||Ce(i)}function nn(i){return!ee(i)||ve(i).position==="fixed"?null:i.offsetParent}function jr(i){var n=/firefox/i.test(ii()),o=/Trident/i.test(ii());if(o&&ee(i)){var l=ve(i);if(l.position==="fixed")return null}var a=jt(i);for(oi(a)&&(a=a.host);ee(a)&&["html","body"].indexOf(ue(a))<0;){var h=ve(a);if(h.transform!=="none"||h.perspective!=="none"||h.contain==="paint"||["transform","perspective"].indexOf(h.willChange)!==-1||n&&h.willChange==="filter"||n&&h.filter&&h.filter!=="none")return a;a=a.parentNode}return null}function gt(i){for(var n=Z(i),o=nn(i);o&&Nr(o)&&ve(o).position==="static";)o=nn(o);return o&&(ue(o)==="html"||ue(o)==="body"&&ve(o).position==="static")?n:o||jr(i)||n}function ci(i){return["top","bottom"].indexOf(i)>=0?"x":"y"}function ft(i,n,o){return Me(i,It(n,o))}function Mr(i,n,o){var l=ft(i,n,o);return l>o?o:l}function Tn(){return{top:0,right:0,bottom:0,left:0}}function xn(i){return Object.assign({},Tn(),i)}function kn(i,n){return n.reduce(function(o,l){return o[l]=i,o},{})}var Fr=function(n,o){return n=typeof n=="function"?n(Object.assign({},o.rects,{placement:o.placement})):n,xn(typeof n!="number"?n:kn(n,tt))};function Br(i){var n,o=i.state,l=i.name,a=i.options,h=o.elements.arrow,m=o.modifiersData.popperOffsets,d=ce(o.placement),f=ci(d),b=[z,G].indexOf(d)>=0,g=b?"height":"width";if(!(!h||!m)){var E=Fr(a.padding,o),k=li(h),w=f==="y"?W:z,L=f==="y"?Q:G,O=o.rects.reference[g]+o.rects.reference[f]-m[f]-o.rects.popper[g],C=m[f]-o.rects.reference[f],S=gt(h),P=S?f==="y"?S.clientHeight||0:S.clientWidth||0:0,x=O/2-C/2,y=E[w],A=P-k[g]-E[L],$=P/2-k[g]/2+x,D=ft(y,$,A),F=f;o.modifiersData[l]=(n={},n[F]=D,n.centerOffset=D-$,n)}}function Rr(i){var n=i.state,o=i.options,l=o.element,a=l===void 0?"[data-popper-arrow]":l;a!=null&&(typeof a=="string"&&(a=n.elements.popper.querySelector(a),!a)||On(n.elements.popper,a)&&(n.elements.arrow=a))}const $n={name:"arrow",enabled:!0,phase:"main",fn:Br,effect:Rr,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Je(i){return i.split("-")[1]}var Hr={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Wr(i,n){var o=i.x,l=i.y,a=n.devicePixelRatio||1;return{x:Ge(o*a)/a||0,y:Ge(l*a)/a||0}}function sn(i){var n,o=i.popper,l=i.popperRect,a=i.placement,h=i.variation,m=i.offsets,d=i.position,f=i.gpuAcceleration,b=i.adaptive,g=i.roundOffsets,E=i.isFixed,k=m.x,w=k===void 0?0:k,L=m.y,O=L===void 0?0:L,C=typeof g=="function"?g({x:w,y:O}):{x:w,y:O};w=C.x,O=C.y;var S=m.hasOwnProperty("x"),P=m.hasOwnProperty("y"),x=z,y=W,A=window;if(b){var $=gt(o),D="clientHeight",F="clientWidth";if($===Z(o)&&($=Ce(o),ve($).position!=="static"&&d==="absolute"&&(D="scrollHeight",F="scrollWidth")),$=$,a===W||(a===z||a===G)&&h===Qe){y=Q;var j=E&&$===A&&A.visualViewport?A.visualViewport.height:$[D];O-=j-l.height,O*=f?1:-1}if(a===z||(a===W||a===Q)&&h===Qe){x=G;var N=E&&$===A&&A.visualViewport?A.visualViewport.width:$[F];w-=N-l.width,w*=f?1:-1}}var M=Object.assign({position:d},b&&Hr),q=g===!0?Wr({x:w,y:O},Z(o)):{x:w,y:O};if(w=q.x,O=q.y,f){var B;return Object.assign({},M,(B={},B[y]=P?"0":"",B[x]=S?"0":"",B.transform=(A.devicePixelRatio||1)<=1?"translate("+w+"px, "+O+"px)":"translate3d("+w+"px, "+O+"px, 0)",B))}return Object.assign({},M,(n={},n[y]=P?O+"px":"",n[x]=S?w+"px":"",n.transform="",n))}function zr(i){var n=i.state,o=i.options,l=o.gpuAcceleration,a=l===void 0?!0:l,h=o.adaptive,m=h===void 0?!0:h,d=o.roundOffsets,f=d===void 0?!0:d,b={placement:ce(n.placement),variation:Je(n.placement),popper:n.elements.popper,popperRect:n.rects.popper,gpuAcceleration:a,isFixed:n.options.strategy==="fixed"};n.modifiersData.popperOffsets!=null&&(n.styles.popper=Object.assign({},n.styles.popper,sn(Object.assign({},b,{offsets:n.modifiersData.popperOffsets,position:n.options.strategy,adaptive:m,roundOffsets:f})))),n.modifiersData.arrow!=null&&(n.styles.arrow=Object.assign({},n.styles.arrow,sn(Object.assign({},b,{offsets:n.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:f})))),n.attributes.popper=Object.assign({},n.attributes.popper,{"data-popper-placement":n.placement})}const ui={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:zr,data:{}};var Lt={passive:!0};function qr(i){var n=i.state,o=i.instance,l=i.options,a=l.scroll,h=a===void 0?!0:a,m=l.resize,d=m===void 0?!0:m,f=Z(n.elements.popper),b=[].concat(n.scrollParents.reference,n.scrollParents.popper);return h&&b.forEach(function(g){g.addEventListener("scroll",o.update,Lt)}),d&&f.addEventListener("resize",o.update,Lt),function(){h&&b.forEach(function(g){g.removeEventListener("scroll",o.update,Lt)}),d&&f.removeEventListener("resize",o.update,Lt)}}const hi={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:qr,data:{}};var Vr={left:"right",right:"left",bottom:"top",top:"bottom"};function Pt(i){return i.replace(/left|right|bottom|top/g,function(n){return Vr[n]})}var Xr={start:"end",end:"start"};function rn(i){return i.replace(/start|end/g,function(n){return Xr[n]})}function di(i){var n=Z(i),o=n.pageXOffset,l=n.pageYOffset;return{scrollLeft:o,scrollTop:l}}function fi(i){return Ze(Ce(i)).left+di(i).scrollLeft}function Yr(i,n){var o=Z(i),l=Ce(i),a=o.visualViewport,h=l.clientWidth,m=l.clientHeight,d=0,f=0;if(a){h=a.width,m=a.height;var b=Cn();(b||!b&&n==="fixed")&&(d=a.offsetLeft,f=a.offsetTop)}return{width:h,height:m,x:d+fi(i),y:f}}function Kr(i){var n,o=Ce(i),l=di(i),a=(n=i.ownerDocument)==null?void 0:n.body,h=Me(o.scrollWidth,o.clientWidth,a?a.scrollWidth:0,a?a.clientWidth:0),m=Me(o.scrollHeight,o.clientHeight,a?a.scrollHeight:0,a?a.clientHeight:0),d=-l.scrollLeft+fi(i),f=-l.scrollTop;return ve(a||o).direction==="rtl"&&(d+=Me(o.clientWidth,a?a.clientWidth:0)-h),{width:h,height:m,x:d,y:f}}function pi(i){var n=ve(i),o=n.overflow,l=n.overflowX,a=n.overflowY;return/auto|scroll|overlay|hidden/.test(o+a+l)}function Sn(i){return["html","body","#document"].indexOf(ue(i))>=0?i.ownerDocument.body:ee(i)&&pi(i)?i:Sn(jt(i))}function pt(i,n){var o;n===void 0&&(n=[]);var l=Sn(i),a=l===((o=i.ownerDocument)==null?void 0:o.body),h=Z(l),m=a?[h].concat(h.visualViewport||[],pi(l)?l:[]):l,d=n.concat(m);return a?d:d.concat(pt(jt(m)))}function ni(i){return Object.assign({},i,{left:i.x,top:i.y,right:i.x+i.width,bottom:i.y+i.height})}function Ur(i,n){var o=Ze(i,!1,n==="fixed");return o.top=o.top+i.clientTop,o.left=o.left+i.clientLeft,o.bottom=o.top+i.clientHeight,o.right=o.left+i.clientWidth,o.width=i.clientWidth,o.height=i.clientHeight,o.x=o.left,o.y=o.top,o}function on(i,n,o){return n===si?ni(Yr(i,o)):Be(n)?Ur(n,o):ni(Kr(Ce(i)))}function Qr(i){var n=pt(jt(i)),o=["absolute","fixed"].indexOf(ve(i).position)>=0,l=o&&ee(i)?gt(i):i;return Be(l)?n.filter(function(a){return Be(a)&&On(a,l)&&ue(a)!=="body"}):[]}function Gr(i,n,o,l){var a=n==="clippingParents"?Qr(i):[].concat(n),h=[].concat(a,[o]),m=h[0],d=h.reduce(function(f,b){var g=on(i,b,l);return f.top=Me(g.top,f.top),f.right=It(g.right,f.right),f.bottom=It(g.bottom,f.bottom),f.left=Me(g.left,f.left),f},on(i,m,l));return d.width=d.right-d.left,d.height=d.bottom-d.top,d.x=d.left,d.y=d.top,d}function Ln(i){var n=i.reference,o=i.element,l=i.placement,a=l?ce(l):null,h=l?Je(l):null,m=n.x+n.width/2-o.width/2,d=n.y+n.height/2-o.height/2,f;switch(a){case W:f={x:m,y:n.y-o.height};break;case Q:f={x:m,y:n.y+n.height};break;case G:f={x:n.x+n.width,y:d};break;case z:f={x:n.x-o.width,y:d};break;default:f={x:n.x,y:n.y}}var b=a?ci(a):null;if(b!=null){var g=b==="y"?"height":"width";switch(h){case Fe:f[b]=f[b]-(n[g]/2-o[g]/2);break;case Qe:f[b]=f[b]+(n[g]/2-o[g]/2);break}}return f}function et(i,n){n===void 0&&(n={});var o=n,l=o.placement,a=l===void 0?i.placement:l,h=o.strategy,m=h===void 0?i.strategy:h,d=o.boundary,f=d===void 0?dn:d,b=o.rootBoundary,g=b===void 0?si:b,E=o.elementContext,k=E===void 0?Ue:E,w=o.altBoundary,L=w===void 0?!1:w,O=o.padding,C=O===void 0?0:O,S=xn(typeof C!="number"?C:kn(C,tt)),P=k===Ue?fn:Ue,x=i.rects.popper,y=i.elements[L?P:k],A=Gr(Be(y)?y:y.contextElement||Ce(i.elements.popper),f,g,m),$=Ze(i.elements.reference),D=Ln({reference:$,element:x,placement:a}),F=ni(Object.assign({},x,D)),j=k===Ue?F:$,N={top:A.top-j.top+S.top,bottom:j.bottom-A.bottom+S.bottom,left:A.left-j.left+S.left,right:j.right-A.right+S.right},M=i.modifiersData.offset;if(k===Ue&&M){var q=M[a];Object.keys(N).forEach(function(B){var he=[G,Q].indexOf(B)>=0?1:-1,oe=[W,Q].indexOf(B)>=0?"y":"x";N[B]+=q[oe]*he})}return N}function Zr(i,n){n===void 0&&(n={});var o=n,l=o.placement,a=o.boundary,h=o.rootBoundary,m=o.padding,d=o.flipVariations,f=o.allowedAutoPlacements,b=f===void 0?ri:f,g=Je(l),E=g?d?ti:ti.filter(function(L){return Je(L)===g}):tt,k=E.filter(function(L){return b.indexOf(L)>=0});k.length===0&&(k=E);var w=k.reduce(function(L,O){return L[O]=et(i,{placement:O,boundary:a,rootBoundary:h,padding:m})[ce(O)],L},{});return Object.keys(w).sort(function(L,O){return w[L]-w[O]})}function Jr(i){if(ce(i)===Nt)return[];var n=Pt(i);return[rn(i),n,rn(n)]}function eo(i){var n=i.state,o=i.options,l=i.name;if(!n.modifiersData[l]._skip){for(var a=o.mainAxis,h=a===void 0?!0:a,m=o.altAxis,d=m===void 0?!0:m,f=o.fallbackPlacements,b=o.padding,g=o.boundary,E=o.rootBoundary,k=o.altBoundary,w=o.flipVariations,L=w===void 0?!0:w,O=o.allowedAutoPlacements,C=n.options.placement,S=ce(C),P=S===C,x=f||(P||!L?[Pt(C)]:Jr(C)),y=[C].concat(x).reduce(function(le,te){return le.concat(ce(te)===Nt?Zr(n,{placement:te,boundary:g,rootBoundary:E,padding:b,flipVariations:L,allowedAutoPlacements:O}):te)},[]),A=n.rects.reference,$=n.rects.popper,D=new Map,F=!0,j=y[0],N=0;N<y.length;N++){var M=y[N],q=ce(M),B=Je(M)===Fe,he=[W,Q].indexOf(q)>=0,oe=he?"width":"height",R=et(n,{placement:M,boundary:g,rootBoundary:E,altBoundary:k,padding:b}),V=he?B?G:z:B?Q:W;A[oe]>$[oe]&&(V=Pt(V));var Oe=Pt(V),ae=[];if(h&&ae.push(R[q]<=0),d&&ae.push(R[V]<=0,R[Oe]<=0),ae.every(function(le){return le})){j=M,F=!1;break}D.set(M,ae)}if(F)for(var be=L?3:1,it=function(te){var de=y.find(function(X){var K=D.get(X);if(K)return K.slice(0,te).every(function(H){return H})});if(de)return j=de,"break"},ye=be;ye>0;ye--){var u=it(ye);if(u==="break")break}n.placement!==j&&(n.modifiersData[l]._skip=!0,n.placement=j,n.reset=!0)}}const Dn={name:"flip",enabled:!0,phase:"main",fn:eo,requiresIfExists:["offset"],data:{_skip:!1}};function an(i,n,o){return o===void 0&&(o={x:0,y:0}),{top:i.top-n.height-o.y,right:i.right-n.width+o.x,bottom:i.bottom-n.height+o.y,left:i.left-n.width-o.x}}function ln(i){return[W,G,Q,z].some(function(n){return i[n]>=0})}function to(i){var n=i.state,o=i.name,l=n.rects.reference,a=n.rects.popper,h=n.modifiersData.preventOverflow,m=et(n,{elementContext:"reference"}),d=et(n,{altBoundary:!0}),f=an(m,l),b=an(d,a,h),g=ln(f),E=ln(b);n.modifiersData[o]={referenceClippingOffsets:f,popperEscapeOffsets:b,isReferenceHidden:g,hasPopperEscaped:E},n.attributes.popper=Object.assign({},n.attributes.popper,{"data-popper-reference-hidden":g,"data-popper-escaped":E})}const Pn={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:to};function io(i,n,o){var l=ce(i),a=[z,W].indexOf(l)>=0?-1:1,h=typeof o=="function"?o(Object.assign({},n,{placement:i})):o,m=h[0],d=h[1];return m=m||0,d=(d||0)*a,[z,G].indexOf(l)>=0?{x:d,y:m}:{x:m,y:d}}function no(i){var n=i.state,o=i.options,l=i.name,a=o.offset,h=a===void 0?[0,0]:a,m=ri.reduce(function(g,E){return g[E]=io(E,n.rects,h),g},{}),d=m[n.placement],f=d.x,b=d.y;n.modifiersData.popperOffsets!=null&&(n.modifiersData.popperOffsets.x+=f,n.modifiersData.popperOffsets.y+=b),n.modifiersData[l]=m}const In={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:no};function so(i){var n=i.state,o=i.name;n.modifiersData[o]=Ln({reference:n.rects.reference,element:n.rects.popper,placement:n.placement})}const gi={name:"popperOffsets",enabled:!0,phase:"read",fn:so,data:{}};function ro(i){return i==="x"?"y":"x"}function oo(i){var n=i.state,o=i.options,l=i.name,a=o.mainAxis,h=a===void 0?!0:a,m=o.altAxis,d=m===void 0?!1:m,f=o.boundary,b=o.rootBoundary,g=o.altBoundary,E=o.padding,k=o.tether,w=k===void 0?!0:k,L=o.tetherOffset,O=L===void 0?0:L,C=et(n,{boundary:f,rootBoundary:b,padding:E,altBoundary:g}),S=ce(n.placement),P=Je(n.placement),x=!P,y=ci(S),A=ro(y),$=n.modifiersData.popperOffsets,D=n.rects.reference,F=n.rects.popper,j=typeof O=="function"?O(Object.assign({},n.rects,{placement:n.placement})):O,N=typeof j=="number"?{mainAxis:j,altAxis:j}:Object.assign({mainAxis:0,altAxis:0},j),M=n.modifiersData.offset?n.modifiersData.offset[n.placement]:null,q={x:0,y:0};if($){if(h){var B,he=y==="y"?W:z,oe=y==="y"?Q:G,R=y==="y"?"height":"width",V=$[y],Oe=V+C[he],ae=V-C[oe],be=w?-F[R]/2:0,it=P===Fe?D[R]:F[R],ye=P===Fe?-F[R]:-D[R],u=n.elements.arrow,le=w&&u?li(u):{width:0,height:0},te=n.modifiersData["arrow#persistent"]?n.modifiersData["arrow#persistent"].padding:Tn(),de=te[he],X=te[oe],K=ft(0,D[R],le[R]),H=x?D[R]/2-be-K-de-N.mainAxis:it-K-de-N.mainAxis,nt=x?-D[R]/2+be+K+X+N.mainAxis:ye+K+X+N.mainAxis,v=n.elements.arrow&&gt(n.elements.arrow),Re=v?y==="y"?v.clientTop||0:v.clientLeft||0:0,st=(B=M?.[y])!=null?B:0,Ft=V+H-st-Re,Bt=V+nt-st,we=ft(w?It(Oe,Ft):Oe,V,w?Me(ae,Bt):ae);$[y]=we,q[y]=we-V}if(d){var rt,Te=y==="x"?W:z,Ae=y==="x"?Q:G,fe=$[A],He=A==="y"?"height":"width",mt=fe+C[Te],_t=fe-C[Ae],ot=[W,z].indexOf(S)!==-1,vt=(rt=M?.[A])!=null?rt:0,bt=ot?mt:fe-D[He]-F[He]-vt+N.altAxis,xe=ot?fe+D[He]+F[He]-vt-N.altAxis:_t,ie=w&&ot?Mr(bt,fe,xe):ft(w?bt:mt,fe,w?xe:_t);$[A]=ie,q[A]=ie-fe}n.modifiersData[l]=q}}const Nn={name:"preventOverflow",enabled:!0,phase:"main",fn:oo,requiresIfExists:["offset"]};function ao(i){return{scrollLeft:i.scrollLeft,scrollTop:i.scrollTop}}function lo(i){return i===Z(i)||!ee(i)?di(i):ao(i)}function co(i){var n=i.getBoundingClientRect(),o=Ge(n.width)/i.offsetWidth||1,l=Ge(n.height)/i.offsetHeight||1;return o!==1||l!==1}function uo(i,n,o){o===void 0&&(o=!1);var l=ee(n),a=ee(n)&&co(n),h=Ce(n),m=Ze(i,a,o),d={scrollLeft:0,scrollTop:0},f={x:0,y:0};return(l||!l&&!o)&&((ue(n)!=="body"||pi(h))&&(d=lo(n)),ee(n)?(f=Ze(n,!0),f.x+=n.clientLeft,f.y+=n.clientTop):h&&(f.x=fi(h))),{x:m.left+d.scrollLeft-f.x,y:m.top+d.scrollTop-f.y,width:m.width,height:m.height}}function ho(i){var n=new Map,o=new Set,l=[];i.forEach(function(h){n.set(h.name,h)});function a(h){o.add(h.name);var m=[].concat(h.requires||[],h.requiresIfExists||[]);m.forEach(function(d){if(!o.has(d)){var f=n.get(d);f&&a(f)}}),l.push(h)}return i.forEach(function(h){o.has(h.name)||a(h)}),l}function fo(i){var n=ho(i);return En.reduce(function(o,l){return o.concat(n.filter(function(a){return a.phase===l}))},[])}function po(i){var n;return function(){return n||(n=new Promise(function(o){Promise.resolve().then(function(){n=void 0,o(i())})})),n}}function go(i){var n=i.reduce(function(o,l){var a=o[l.name];return o[l.name]=a?Object.assign({},a,l,{options:Object.assign({},a.options,l.options),data:Object.assign({},a.data,l.data)}):l,o},{});return Object.keys(n).map(function(o){return n[o]})}var cn={placement:"bottom",modifiers:[],strategy:"absolute"};function un(){for(var i=arguments.length,n=new Array(i),o=0;o<i;o++)n[o]=arguments[o];return!n.some(function(l){return!(l&&typeof l.getBoundingClientRect=="function")})}function Mt(i){i===void 0&&(i={});var n=i,o=n.defaultModifiers,l=o===void 0?[]:o,a=n.defaultOptions,h=a===void 0?cn:a;return function(d,f,b){b===void 0&&(b=h);var g={placement:"bottom",orderedModifiers:[],options:Object.assign({},cn,h),modifiersData:{},elements:{reference:d,popper:f},attributes:{},styles:{}},E=[],k=!1,w={state:g,setOptions:function(S){var P=typeof S=="function"?S(g.options):S;O(),g.options=Object.assign({},h,g.options,P),g.scrollParents={reference:Be(d)?pt(d):d.contextElement?pt(d.contextElement):[],popper:pt(f)};var x=fo(go([].concat(l,g.options.modifiers)));return g.orderedModifiers=x.filter(function(y){return y.enabled}),L(),w.update()},forceUpdate:function(){if(!k){var S=g.elements,P=S.reference,x=S.popper;if(un(P,x)){g.rects={reference:uo(P,gt(x),g.options.strategy==="fixed"),popper:li(x)},g.reset=!1,g.placement=g.options.placement,g.orderedModifiers.forEach(function(N){return g.modifiersData[N.name]=Object.assign({},N.data)});for(var y=0;y<g.orderedModifiers.length;y++){if(g.reset===!0){g.reset=!1,y=-1;continue}var A=g.orderedModifiers[y],$=A.fn,D=A.options,F=D===void 0?{}:D,j=A.name;typeof $=="function"&&(g=$({state:g,options:F,name:j,instance:w})||g)}}}},update:po(function(){return new Promise(function(C){w.forceUpdate(),C(g)})}),destroy:function(){O(),k=!0}};if(!un(d,f))return w;w.setOptions(b).then(function(C){!k&&b.onFirstUpdate&&b.onFirstUpdate(C)});function L(){g.orderedModifiers.forEach(function(C){var S=C.name,P=C.options,x=P===void 0?{}:P,y=C.effect;if(typeof y=="function"){var A=y({state:g,name:S,instance:w,options:x}),$=function(){};E.push(A||$)}})}function O(){E.forEach(function(C){return C()}),E=[]}return w}}var mo=Mt(),_o=[hi,gi,ui,ai],vo=Mt({defaultModifiers:_o}),bo=[hi,gi,ui,ai,In,Dn,Nn,$n,Pn],yo=Mt({defaultModifiers:bo});const wo=Object.freeze(Object.defineProperty({__proto__:null,afterMain:bn,afterRead:mn,afterWrite:An,applyStyles:ai,arrow:$n,auto:Nt,basePlacements:tt,beforeMain:_n,beforeRead:pn,beforeWrite:yn,bottom:Q,clippingParents:dn,computeStyles:ui,createPopper:yo,createPopperBase:mo,createPopperLite:vo,detectOverflow:et,end:Qe,eventListeners:hi,flip:Dn,hide:Pn,left:z,main:vn,modifierPhases:En,offset:In,placements:ri,popper:Ue,popperGenerator:Mt,popperOffsets:gi,preventOverflow:Nn,read:gn,reference:fn,right:G,start:Fe,top:W,variationPlacements:ti,viewport:si,write:wn},Symbol.toStringTag,{value:"Module"})),Ao=Dr(wo);/*!
  * Bootstrap v5.3.7 (https://getbootstrap.com/)
  * Copyright 2011-2025 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */var Eo=Dt.exports,hn;function Co(){return hn||(hn=1,function(i,n){(function(o,l){i.exports=l(Ao)})(Eo,function(o){function l(r){const e=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(r){for(const t in r)if(t!=="default"){const s=Object.getOwnPropertyDescriptor(r,t);Object.defineProperty(e,t,s.get?s:{enumerable:!0,get:()=>r[t]})}}return e.default=r,Object.freeze(e)}const a=l(o),h=new Map,m={set(r,e,t){h.has(r)||h.set(r,new Map);const s=h.get(r);s.has(e)||s.size===0?s.set(e,t):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(s.keys())[0]}.`)},get:(r,e)=>h.has(r)&&h.get(r).get(e)||null,remove(r,e){if(!h.has(r))return;const t=h.get(r);t.delete(e),t.size===0&&h.delete(r)}},d="transitionend",f=r=>(r&&window.CSS&&window.CSS.escape&&(r=r.replace(/#([^\s"#']+)/g,(e,t)=>`#${CSS.escape(t)}`)),r),b=r=>{r.dispatchEvent(new Event(d))},g=r=>!(!r||typeof r!="object")&&(r.jquery!==void 0&&(r=r[0]),r.nodeType!==void 0),E=r=>g(r)?r.jquery?r[0]:r:typeof r=="string"&&r.length>0?document.querySelector(f(r)):null,k=r=>{if(!g(r)||r.getClientRects().length===0)return!1;const e=getComputedStyle(r).getPropertyValue("visibility")==="visible",t=r.closest("details:not([open])");if(!t)return e;if(t!==r){const s=r.closest("summary");if(s&&s.parentNode!==t||s===null)return!1}return e},w=r=>!r||r.nodeType!==Node.ELEMENT_NODE||!!r.classList.contains("disabled")||(r.disabled!==void 0?r.disabled:r.hasAttribute("disabled")&&r.getAttribute("disabled")!=="false"),L=r=>{if(!document.documentElement.attachShadow)return null;if(typeof r.getRootNode=="function"){const e=r.getRootNode();return e instanceof ShadowRoot?e:null}return r instanceof ShadowRoot?r:r.parentNode?L(r.parentNode):null},O=()=>{},C=r=>{r.offsetHeight},S=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,P=[],x=()=>document.documentElement.dir==="rtl",y=r=>{var e;e=()=>{const t=S();if(t){const s=r.NAME,c=t.fn[s];t.fn[s]=r.jQueryInterface,t.fn[s].Constructor=r,t.fn[s].noConflict=()=>(t.fn[s]=c,r.jQueryInterface)}},document.readyState==="loading"?(P.length||document.addEventListener("DOMContentLoaded",()=>{for(const t of P)t()}),P.push(e)):e()},A=(r,e=[],t=r)=>typeof r=="function"?r.call(...e):t,$=(r,e,t=!0)=>{if(!t)return void A(r);const s=(_=>{if(!_)return 0;let{transitionDuration:T,transitionDelay:I}=window.getComputedStyle(_);const Y=Number.parseFloat(T),U=Number.parseFloat(I);return Y||U?(T=T.split(",")[0],I=I.split(",")[0],1e3*(Number.parseFloat(T)+Number.parseFloat(I))):0})(e)+5;let c=!1;const p=({target:_})=>{_===e&&(c=!0,e.removeEventListener(d,p),A(r))};e.addEventListener(d,p),setTimeout(()=>{c||b(e)},s)},D=(r,e,t,s)=>{const c=r.length;let p=r.indexOf(e);return p===-1?!t&&s?r[c-1]:r[0]:(p+=t?1:-1,s&&(p=(p+c)%c),r[Math.max(0,Math.min(p,c-1))])},F=/[^.]*(?=\..*)\.|.*/,j=/\..*/,N=/::\d+$/,M={};let q=1;const B={mouseenter:"mouseover",mouseleave:"mouseout"},he=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function oe(r,e){return e&&`${e}::${q++}`||r.uidEvent||q++}function R(r){const e=oe(r);return r.uidEvent=e,M[e]=M[e]||{},M[e]}function V(r,e,t=null){return Object.values(r).find(s=>s.callable===e&&s.delegationSelector===t)}function Oe(r,e,t){const s=typeof e=="string",c=s?t:e||t;let p=ye(r);return he.has(p)||(p=r),[s,c,p]}function ae(r,e,t,s,c){if(typeof e!="string"||!r)return;let[p,_,T]=Oe(e,t,s);e in B&&(_=(Ne=>function(re){if(!re.relatedTarget||re.relatedTarget!==re.delegateTarget&&!re.delegateTarget.contains(re.relatedTarget))return Ne.call(this,re)})(_));const I=R(r),Y=I[T]||(I[T]={}),U=V(Y,_,p?t:null);if(U)return void(U.oneOff=U.oneOff&&c);const se=oe(_,e.replace(F,"")),me=p?function(_e,Ne,re){return function dt(St){const Sr=_e.querySelectorAll(Ne);for(let{target:je}=St;je&&je!==this;je=je.parentNode)for(const Lr of Sr)if(Lr===je)return le(St,{delegateTarget:je}),dt.oneOff&&u.off(_e,St.type,Ne,re),re.apply(je,[St])}}(r,t,_):function(_e,Ne){return function re(dt){return le(dt,{delegateTarget:_e}),re.oneOff&&u.off(_e,dt.type,Ne),Ne.apply(_e,[dt])}}(r,_);me.delegationSelector=p?t:null,me.callable=_,me.oneOff=c,me.uidEvent=se,Y[se]=me,r.addEventListener(T,me,p)}function be(r,e,t,s,c){const p=V(e[t],s,c);p&&(r.removeEventListener(t,p,!!c),delete e[t][p.uidEvent])}function it(r,e,t,s){const c=e[t]||{};for(const[p,_]of Object.entries(c))p.includes(s)&&be(r,e,t,_.callable,_.delegationSelector)}function ye(r){return r=r.replace(j,""),B[r]||r}const u={on(r,e,t,s){ae(r,e,t,s,!1)},one(r,e,t,s){ae(r,e,t,s,!0)},off(r,e,t,s){if(typeof e!="string"||!r)return;const[c,p,_]=Oe(e,t,s),T=_!==e,I=R(r),Y=I[_]||{},U=e.startsWith(".");if(p===void 0){if(U)for(const se of Object.keys(I))it(r,I,se,e.slice(1));for(const[se,me]of Object.entries(Y)){const _e=se.replace(N,"");T&&!e.includes(_e)||be(r,I,_,me.callable,me.delegationSelector)}}else{if(!Object.keys(Y).length)return;be(r,I,_,p,c?t:null)}},trigger(r,e,t){if(typeof e!="string"||!r)return null;const s=S();let c=null,p=!0,_=!0,T=!1;e!==ye(e)&&s&&(c=s.Event(e,t),s(r).trigger(c),p=!c.isPropagationStopped(),_=!c.isImmediatePropagationStopped(),T=c.isDefaultPrevented());const I=le(new Event(e,{bubbles:p,cancelable:!0}),t);return T&&I.preventDefault(),_&&r.dispatchEvent(I),I.defaultPrevented&&c&&c.preventDefault(),I}};function le(r,e={}){for(const[t,s]of Object.entries(e))try{r[t]=s}catch{Object.defineProperty(r,t,{configurable:!0,get:()=>s})}return r}function te(r){if(r==="true")return!0;if(r==="false")return!1;if(r===Number(r).toString())return Number(r);if(r===""||r==="null")return null;if(typeof r!="string")return r;try{return JSON.parse(decodeURIComponent(r))}catch{return r}}function de(r){return r.replace(/[A-Z]/g,e=>`-${e.toLowerCase()}`)}const X={setDataAttribute(r,e,t){r.setAttribute(`data-bs-${de(e)}`,t)},removeDataAttribute(r,e){r.removeAttribute(`data-bs-${de(e)}`)},getDataAttributes(r){if(!r)return{};const e={},t=Object.keys(r.dataset).filter(s=>s.startsWith("bs")&&!s.startsWith("bsConfig"));for(const s of t){let c=s.replace(/^bs/,"");c=c.charAt(0).toLowerCase()+c.slice(1),e[c]=te(r.dataset[s])}return e},getDataAttribute:(r,e)=>te(r.getAttribute(`data-bs-${de(e)}`))};class K{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e}_mergeConfigObj(e,t){const s=g(t)?X.getDataAttribute(t,"config"):{};return{...this.constructor.Default,...typeof s=="object"?s:{},...g(t)?X.getDataAttributes(t):{},...typeof e=="object"?e:{}}}_typeCheckConfig(e,t=this.constructor.DefaultType){for(const[c,p]of Object.entries(t)){const _=e[c],T=g(_)?"element":(s=_)==null?`${s}`:Object.prototype.toString.call(s).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(p).test(T))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${c}" provided type "${T}" but expected type "${p}".`)}var s}}class H extends K{constructor(e,t){super(),(e=E(e))&&(this._element=e,this._config=this._getConfig(t),m.set(this._element,this.constructor.DATA_KEY,this))}dispose(){m.remove(this._element,this.constructor.DATA_KEY),u.off(this._element,this.constructor.EVENT_KEY);for(const e of Object.getOwnPropertyNames(this))this[e]=null}_queueCallback(e,t,s=!0){$(e,t,s)}_getConfig(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}static getInstance(e){return m.get(E(e),this.DATA_KEY)}static getOrCreateInstance(e,t={}){return this.getInstance(e)||new this(e,typeof t=="object"?t:null)}static get VERSION(){return"5.3.7"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(e){return`${e}${this.EVENT_KEY}`}}const nt=r=>{let e=r.getAttribute("data-bs-target");if(!e||e==="#"){let t=r.getAttribute("href");if(!t||!t.includes("#")&&!t.startsWith("."))return null;t.includes("#")&&!t.startsWith("#")&&(t=`#${t.split("#")[1]}`),e=t&&t!=="#"?t.trim():null}return e?e.split(",").map(t=>f(t)).join(","):null},v={find:(r,e=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(e,r)),findOne:(r,e=document.documentElement)=>Element.prototype.querySelector.call(e,r),children:(r,e)=>[].concat(...r.children).filter(t=>t.matches(e)),parents(r,e){const t=[];let s=r.parentNode.closest(e);for(;s;)t.push(s),s=s.parentNode.closest(e);return t},prev(r,e){let t=r.previousElementSibling;for(;t;){if(t.matches(e))return[t];t=t.previousElementSibling}return[]},next(r,e){let t=r.nextElementSibling;for(;t;){if(t.matches(e))return[t];t=t.nextElementSibling}return[]},focusableChildren(r){const e=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(t=>`${t}:not([tabindex^="-"])`).join(",");return this.find(e,r).filter(t=>!w(t)&&k(t))},getSelectorFromElement(r){const e=nt(r);return e&&v.findOne(e)?e:null},getElementFromSelector(r){const e=nt(r);return e?v.findOne(e):null},getMultipleElementsFromSelector(r){const e=nt(r);return e?v.find(e):[]}},Re=(r,e="hide")=>{const t=`click.dismiss${r.EVENT_KEY}`,s=r.NAME;u.on(document,t,`[data-bs-dismiss="${s}"]`,function(c){if(["A","AREA"].includes(this.tagName)&&c.preventDefault(),w(this))return;const p=v.getElementFromSelector(this)||this.closest(`.${s}`);r.getOrCreateInstance(p)[e]()})},st=".bs.alert",Ft=`close${st}`,Bt=`closed${st}`;class we extends H{static get NAME(){return"alert"}close(){if(u.trigger(this._element,Ft).defaultPrevented)return;this._element.classList.remove("show");const e=this._element.classList.contains("fade");this._queueCallback(()=>this._destroyElement(),this._element,e)}_destroyElement(){this._element.remove(),u.trigger(this._element,Bt),this.dispose()}static jQueryInterface(e){return this.each(function(){const t=we.getOrCreateInstance(this);if(typeof e=="string"){if(t[e]===void 0||e.startsWith("_")||e==="constructor")throw new TypeError(`No method named "${e}"`);t[e](this)}})}}Re(we,"close"),y(we);const rt='[data-bs-toggle="button"]';class Te extends H{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(e){return this.each(function(){const t=Te.getOrCreateInstance(this);e==="toggle"&&t[e]()})}}u.on(document,"click.bs.button.data-api",rt,r=>{r.preventDefault();const e=r.target.closest(rt);Te.getOrCreateInstance(e).toggle()}),y(Te);const Ae=".bs.swipe",fe=`touchstart${Ae}`,He=`touchmove${Ae}`,mt=`touchend${Ae}`,_t=`pointerdown${Ae}`,ot=`pointerup${Ae}`,vt={endCallback:null,leftCallback:null,rightCallback:null},bt={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class xe extends K{constructor(e,t){super(),this._element=e,e&&xe.isSupported()&&(this._config=this._getConfig(t),this._deltaX=0,this._supportPointerEvents=!!window.PointerEvent,this._initEvents())}static get Default(){return vt}static get DefaultType(){return bt}static get NAME(){return"swipe"}dispose(){u.off(this._element,Ae)}_start(e){this._supportPointerEvents?this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX):this._deltaX=e.touches[0].clientX}_end(e){this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX-this._deltaX),this._handleSwipe(),A(this._config.endCallback)}_move(e){this._deltaX=e.touches&&e.touches.length>1?0:e.touches[0].clientX-this._deltaX}_handleSwipe(){const e=Math.abs(this._deltaX);if(e<=40)return;const t=e/this._deltaX;this._deltaX=0,t&&A(t>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(u.on(this._element,_t,e=>this._start(e)),u.on(this._element,ot,e=>this._end(e)),this._element.classList.add("pointer-event")):(u.on(this._element,fe,e=>this._start(e)),u.on(this._element,He,e=>this._move(e)),u.on(this._element,mt,e=>this._end(e)))}_eventIsPointerPenTouch(e){return this._supportPointerEvents&&(e.pointerType==="pen"||e.pointerType==="touch")}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const ie=".bs.carousel",mi=".data-api",jn="ArrowLeft",Mn="ArrowRight",at="next",We="prev",ze="left",yt="right",Fn=`slide${ie}`,Rt=`slid${ie}`,Bn=`keydown${ie}`,Rn=`mouseenter${ie}`,Hn=`mouseleave${ie}`,Wn=`dragstart${ie}`,zn=`load${ie}${mi}`,qn=`click${ie}${mi}`,_i="carousel",wt="active",vi=".active",bi=".carousel-item",Vn=vi+bi,Xn={[jn]:yt,[Mn]:ze},Yn={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},Kn={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class qe extends H{constructor(e,t){super(e,t),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=v.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===_i&&this.cycle()}static get Default(){return Yn}static get DefaultType(){return Kn}static get NAME(){return"carousel"}next(){this._slide(at)}nextWhenVisible(){!document.hidden&&k(this._element)&&this.next()}prev(){this._slide(We)}pause(){this._isSliding&&b(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?u.one(this._element,Rt,()=>this.cycle()):this.cycle())}to(e){const t=this._getItems();if(e>t.length-1||e<0)return;if(this._isSliding)return void u.one(this._element,Rt,()=>this.to(e));const s=this._getItemIndex(this._getActive());if(s===e)return;const c=e>s?at:We;this._slide(c,t[e])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(e){return e.defaultInterval=e.interval,e}_addEventListeners(){this._config.keyboard&&u.on(this._element,Bn,e=>this._keydown(e)),this._config.pause==="hover"&&(u.on(this._element,Rn,()=>this.pause()),u.on(this._element,Hn,()=>this._maybeEnableCycle())),this._config.touch&&xe.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const t of v.find(".carousel-item img",this._element))u.on(t,Wn,s=>s.preventDefault());const e={leftCallback:()=>this._slide(this._directionToOrder(ze)),rightCallback:()=>this._slide(this._directionToOrder(yt)),endCallback:()=>{this._config.pause==="hover"&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),500+this._config.interval))}};this._swipeHelper=new xe(this._element,e)}_keydown(e){if(/input|textarea/i.test(e.target.tagName))return;const t=Xn[e.key];t&&(e.preventDefault(),this._slide(this._directionToOrder(t)))}_getItemIndex(e){return this._getItems().indexOf(e)}_setActiveIndicatorElement(e){if(!this._indicatorsElement)return;const t=v.findOne(vi,this._indicatorsElement);t.classList.remove(wt),t.removeAttribute("aria-current");const s=v.findOne(`[data-bs-slide-to="${e}"]`,this._indicatorsElement);s&&(s.classList.add(wt),s.setAttribute("aria-current","true"))}_updateInterval(){const e=this._activeElement||this._getActive();if(!e)return;const t=Number.parseInt(e.getAttribute("data-bs-interval"),10);this._config.interval=t||this._config.defaultInterval}_slide(e,t=null){if(this._isSliding)return;const s=this._getActive(),c=e===at,p=t||D(this._getItems(),s,c,this._config.wrap);if(p===s)return;const _=this._getItemIndex(p),T=se=>u.trigger(this._element,se,{relatedTarget:p,direction:this._orderToDirection(e),from:this._getItemIndex(s),to:_});if(T(Fn).defaultPrevented||!s||!p)return;const I=!!this._interval;this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(_),this._activeElement=p;const Y=c?"carousel-item-start":"carousel-item-end",U=c?"carousel-item-next":"carousel-item-prev";p.classList.add(U),C(p),s.classList.add(Y),p.classList.add(Y),this._queueCallback(()=>{p.classList.remove(Y,U),p.classList.add(wt),s.classList.remove(wt,U,Y),this._isSliding=!1,T(Rt)},s,this._isAnimated()),I&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return v.findOne(Vn,this._element)}_getItems(){return v.find(bi,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(e){return x()?e===ze?We:at:e===ze?at:We}_orderToDirection(e){return x()?e===We?ze:yt:e===We?yt:ze}static jQueryInterface(e){return this.each(function(){const t=qe.getOrCreateInstance(this,e);if(typeof e!="number"){if(typeof e=="string"){if(t[e]===void 0||e.startsWith("_")||e==="constructor")throw new TypeError(`No method named "${e}"`);t[e]()}}else t.to(e)})}}u.on(document,qn,"[data-bs-slide], [data-bs-slide-to]",function(r){const e=v.getElementFromSelector(this);if(!e||!e.classList.contains(_i))return;r.preventDefault();const t=qe.getOrCreateInstance(e),s=this.getAttribute("data-bs-slide-to");return s?(t.to(s),void t._maybeEnableCycle()):X.getDataAttribute(this,"slide")==="next"?(t.next(),void t._maybeEnableCycle()):(t.prev(),void t._maybeEnableCycle())}),u.on(window,zn,()=>{const r=v.find('[data-bs-ride="carousel"]');for(const e of r)qe.getOrCreateInstance(e)}),y(qe);const lt=".bs.collapse",Un=`show${lt}`,Qn=`shown${lt}`,Gn=`hide${lt}`,Zn=`hidden${lt}`,Jn=`click${lt}.data-api`,Ht="show",Ve="collapse",At="collapsing",es=`:scope .${Ve} .${Ve}`,Wt='[data-bs-toggle="collapse"]',ts={parent:null,toggle:!0},is={parent:"(null|element)",toggle:"boolean"};class Xe extends H{constructor(e,t){super(e,t),this._isTransitioning=!1,this._triggerArray=[];const s=v.find(Wt);for(const c of s){const p=v.getSelectorFromElement(c),_=v.find(p).filter(T=>T===this._element);p!==null&&_.length&&this._triggerArray.push(c)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return ts}static get DefaultType(){return is}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let e=[];if(this._config.parent&&(e=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter(c=>c!==this._element).map(c=>Xe.getOrCreateInstance(c,{toggle:!1}))),e.length&&e[0]._isTransitioning||u.trigger(this._element,Un).defaultPrevented)return;for(const c of e)c.hide();const t=this._getDimension();this._element.classList.remove(Ve),this._element.classList.add(At),this._element.style[t]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const s=`scroll${t[0].toUpperCase()+t.slice(1)}`;this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(At),this._element.classList.add(Ve,Ht),this._element.style[t]="",u.trigger(this._element,Qn)},this._element,!0),this._element.style[t]=`${this._element[s]}px`}hide(){if(this._isTransitioning||!this._isShown()||u.trigger(this._element,Gn).defaultPrevented)return;const e=this._getDimension();this._element.style[e]=`${this._element.getBoundingClientRect()[e]}px`,C(this._element),this._element.classList.add(At),this._element.classList.remove(Ve,Ht);for(const t of this._triggerArray){const s=v.getElementFromSelector(t);s&&!this._isShown(s)&&this._addAriaAndCollapsedClass([t],!1)}this._isTransitioning=!0,this._element.style[e]="",this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(At),this._element.classList.add(Ve),u.trigger(this._element,Zn)},this._element,!0)}_isShown(e=this._element){return e.classList.contains(Ht)}_configAfterMerge(e){return e.toggle=!!e.toggle,e.parent=E(e.parent),e}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;const e=this._getFirstLevelChildren(Wt);for(const t of e){const s=v.getElementFromSelector(t);s&&this._addAriaAndCollapsedClass([t],this._isShown(s))}}_getFirstLevelChildren(e){const t=v.find(es,this._config.parent);return v.find(e,this._config.parent).filter(s=>!t.includes(s))}_addAriaAndCollapsedClass(e,t){if(e.length)for(const s of e)s.classList.toggle("collapsed",!t),s.setAttribute("aria-expanded",t)}static jQueryInterface(e){const t={};return typeof e=="string"&&/show|hide/.test(e)&&(t.toggle=!1),this.each(function(){const s=Xe.getOrCreateInstance(this,t);if(typeof e=="string"){if(s[e]===void 0)throw new TypeError(`No method named "${e}"`);s[e]()}})}}u.on(document,Jn,Wt,function(r){(r.target.tagName==="A"||r.delegateTarget&&r.delegateTarget.tagName==="A")&&r.preventDefault();for(const e of v.getMultipleElementsFromSelector(this))Xe.getOrCreateInstance(e,{toggle:!1}).toggle()}),y(Xe);const yi="dropdown",ke=".bs.dropdown",zt=".data-api",ns="ArrowUp",wi="ArrowDown",ss=`hide${ke}`,rs=`hidden${ke}`,os=`show${ke}`,as=`shown${ke}`,Ai=`click${ke}${zt}`,Ei=`keydown${ke}${zt}`,ls=`keyup${ke}${zt}`,Ye="show",$e='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',cs=`${$e}.${Ye}`,Et=".dropdown-menu",us=x()?"top-end":"top-start",hs=x()?"top-start":"top-end",ds=x()?"bottom-end":"bottom-start",fs=x()?"bottom-start":"bottom-end",ps=x()?"left-start":"right-start",gs=x()?"right-start":"left-start",ms={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},_s={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class ne extends H{constructor(e,t){super(e,t),this._popper=null,this._parent=this._element.parentNode,this._menu=v.next(this._element,Et)[0]||v.prev(this._element,Et)[0]||v.findOne(Et,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return ms}static get DefaultType(){return _s}static get NAME(){return yi}toggle(){return this._isShown()?this.hide():this.show()}show(){if(w(this._element)||this._isShown())return;const e={relatedTarget:this._element};if(!u.trigger(this._element,os,e).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const t of[].concat(...document.body.children))u.on(t,"mouseover",O);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(Ye),this._element.classList.add(Ye),u.trigger(this._element,as,e)}}hide(){if(w(this._element)||!this._isShown())return;const e={relatedTarget:this._element};this._completeHide(e)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(e){if(!u.trigger(this._element,ss,e).defaultPrevented){if("ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))u.off(t,"mouseover",O);this._popper&&this._popper.destroy(),this._menu.classList.remove(Ye),this._element.classList.remove(Ye),this._element.setAttribute("aria-expanded","false"),X.removeDataAttribute(this._menu,"popper"),u.trigger(this._element,rs,e),this._element.focus()}}_getConfig(e){if(typeof(e=super._getConfig(e)).reference=="object"&&!g(e.reference)&&typeof e.reference.getBoundingClientRect!="function")throw new TypeError(`${yi.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return e}_createPopper(){if(a===void 0)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org/docs/v2/)");let e=this._element;this._config.reference==="parent"?e=this._parent:g(this._config.reference)?e=E(this._config.reference):typeof this._config.reference=="object"&&(e=this._config.reference);const t=this._getPopperConfig();this._popper=a.createPopper(e,this._menu,t)}_isShown(){return this._menu.classList.contains(Ye)}_getPlacement(){const e=this._parent;if(e.classList.contains("dropend"))return ps;if(e.classList.contains("dropstart"))return gs;if(e.classList.contains("dropup-center"))return"top";if(e.classList.contains("dropdown-center"))return"bottom";const t=getComputedStyle(this._menu).getPropertyValue("--bs-position").trim()==="end";return e.classList.contains("dropup")?t?hs:us:t?fs:ds}_detectNavbar(){return this._element.closest(".navbar")!==null}_getOffset(){const{offset:e}=this._config;return typeof e=="string"?e.split(",").map(t=>Number.parseInt(t,10)):typeof e=="function"?t=>e(t,this._element):e}_getPopperConfig(){const e={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||this._config.display==="static")&&(X.setDataAttribute(this._menu,"popper","static"),e.modifiers=[{name:"applyStyles",enabled:!1}]),{...e,...A(this._config.popperConfig,[void 0,e])}}_selectMenuItem({key:e,target:t}){const s=v.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter(c=>k(c));s.length&&D(s,t,e===wi,!s.includes(t)).focus()}static jQueryInterface(e){return this.each(function(){const t=ne.getOrCreateInstance(this,e);if(typeof e=="string"){if(t[e]===void 0)throw new TypeError(`No method named "${e}"`);t[e]()}})}static clearMenus(e){if(e.button===2||e.type==="keyup"&&e.key!=="Tab")return;const t=v.find(cs);for(const s of t){const c=ne.getInstance(s);if(!c||c._config.autoClose===!1)continue;const p=e.composedPath(),_=p.includes(c._menu);if(p.includes(c._element)||c._config.autoClose==="inside"&&!_||c._config.autoClose==="outside"&&_||c._menu.contains(e.target)&&(e.type==="keyup"&&e.key==="Tab"||/input|select|option|textarea|form/i.test(e.target.tagName)))continue;const T={relatedTarget:c._element};e.type==="click"&&(T.clickEvent=e),c._completeHide(T)}}static dataApiKeydownHandler(e){const t=/input|textarea/i.test(e.target.tagName),s=e.key==="Escape",c=[ns,wi].includes(e.key);if(!c&&!s||t&&!s)return;e.preventDefault();const p=this.matches($e)?this:v.prev(this,$e)[0]||v.next(this,$e)[0]||v.findOne($e,e.delegateTarget.parentNode),_=ne.getOrCreateInstance(p);if(c)return e.stopPropagation(),_.show(),void _._selectMenuItem(e);_._isShown()&&(e.stopPropagation(),_.hide(),p.focus())}}u.on(document,Ei,$e,ne.dataApiKeydownHandler),u.on(document,Ei,Et,ne.dataApiKeydownHandler),u.on(document,Ai,ne.clearMenus),u.on(document,ls,ne.clearMenus),u.on(document,Ai,$e,function(r){r.preventDefault(),ne.getOrCreateInstance(this).toggle()}),y(ne);const Ci="backdrop",Oi="show",Ti=`mousedown.bs.${Ci}`,vs={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},bs={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class xi extends K{constructor(e){super(),this._config=this._getConfig(e),this._isAppended=!1,this._element=null}static get Default(){return vs}static get DefaultType(){return bs}static get NAME(){return Ci}show(e){if(!this._config.isVisible)return void A(e);this._append();const t=this._getElement();this._config.isAnimated&&C(t),t.classList.add(Oi),this._emulateAnimation(()=>{A(e)})}hide(e){this._config.isVisible?(this._getElement().classList.remove(Oi),this._emulateAnimation(()=>{this.dispose(),A(e)})):A(e)}dispose(){this._isAppended&&(u.off(this._element,Ti),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const e=document.createElement("div");e.className=this._config.className,this._config.isAnimated&&e.classList.add("fade"),this._element=e}return this._element}_configAfterMerge(e){return e.rootElement=E(e.rootElement),e}_append(){if(this._isAppended)return;const e=this._getElement();this._config.rootElement.append(e),u.on(e,Ti,()=>{A(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(e){$(e,this._getElement(),this._config.isAnimated)}}const Ct=".bs.focustrap",ys=`focusin${Ct}`,ws=`keydown.tab${Ct}`,ki="backward",As={autofocus:!0,trapElement:null},Es={autofocus:"boolean",trapElement:"element"};class $i extends K{constructor(e){super(),this._config=this._getConfig(e),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return As}static get DefaultType(){return Es}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),u.off(document,Ct),u.on(document,ys,e=>this._handleFocusin(e)),u.on(document,ws,e=>this._handleKeydown(e)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,u.off(document,Ct))}_handleFocusin(e){const{trapElement:t}=this._config;if(e.target===document||e.target===t||t.contains(e.target))return;const s=v.focusableChildren(t);s.length===0?t.focus():this._lastTabNavDirection===ki?s[s.length-1].focus():s[0].focus()}_handleKeydown(e){e.key==="Tab"&&(this._lastTabNavDirection=e.shiftKey?ki:"forward")}}const Si=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Li=".sticky-top",Ot="padding-right",Di="margin-right";class qt{constructor(){this._element=document.body}getWidth(){const e=document.documentElement.clientWidth;return Math.abs(window.innerWidth-e)}hide(){const e=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,Ot,t=>t+e),this._setElementAttributes(Si,Ot,t=>t+e),this._setElementAttributes(Li,Di,t=>t-e)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,Ot),this._resetElementAttributes(Si,Ot),this._resetElementAttributes(Li,Di)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(e,t,s){const c=this.getWidth();this._applyManipulationCallback(e,p=>{if(p!==this._element&&window.innerWidth>p.clientWidth+c)return;this._saveInitialAttribute(p,t);const _=window.getComputedStyle(p).getPropertyValue(t);p.style.setProperty(t,`${s(Number.parseFloat(_))}px`)})}_saveInitialAttribute(e,t){const s=e.style.getPropertyValue(t);s&&X.setDataAttribute(e,t,s)}_resetElementAttributes(e,t){this._applyManipulationCallback(e,s=>{const c=X.getDataAttribute(s,t);c!==null?(X.removeDataAttribute(s,t),s.style.setProperty(t,c)):s.style.removeProperty(t)})}_applyManipulationCallback(e,t){if(g(e))t(e);else for(const s of v.find(e,this._element))t(s)}}const J=".bs.modal",Cs=`hide${J}`,Os=`hidePrevented${J}`,Pi=`hidden${J}`,Ii=`show${J}`,Ts=`shown${J}`,xs=`resize${J}`,ks=`click.dismiss${J}`,$s=`mousedown.dismiss${J}`,Ss=`keydown.dismiss${J}`,Ls=`click${J}.data-api`,Ni="modal-open",ji="show",Vt="modal-static",Ds={backdrop:!0,focus:!0,keyboard:!0},Ps={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class Se extends H{constructor(e,t){super(e,t),this._dialog=v.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new qt,this._addEventListeners()}static get Default(){return Ds}static get DefaultType(){return Ps}static get NAME(){return"modal"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||this._isTransitioning||u.trigger(this._element,Ii,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(Ni),this._adjustDialog(),this._backdrop.show(()=>this._showElement(e)))}hide(){this._isShown&&!this._isTransitioning&&(u.trigger(this._element,Cs).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(ji),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated())))}dispose(){u.off(window,J),u.off(this._dialog,J),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new xi({isVisible:!!this._config.backdrop,isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new $i({trapElement:this._element})}_showElement(e){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const t=v.findOne(".modal-body",this._dialog);t&&(t.scrollTop=0),C(this._element),this._element.classList.add(ji),this._queueCallback(()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,u.trigger(this._element,Ts,{relatedTarget:e})},this._dialog,this._isAnimated())}_addEventListeners(){u.on(this._element,Ss,e=>{e.key==="Escape"&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())}),u.on(window,xs,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),u.on(this._element,$s,e=>{u.one(this._element,ks,t=>{this._element===e.target&&this._element===t.target&&(this._config.backdrop!=="static"?this._config.backdrop&&this.hide():this._triggerBackdropTransition())})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(Ni),this._resetAdjustments(),this._scrollBar.reset(),u.trigger(this._element,Pi)})}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(u.trigger(this._element,Os).defaultPrevented)return;const e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._element.style.overflowY;t==="hidden"||this._element.classList.contains(Vt)||(e||(this._element.style.overflowY="hidden"),this._element.classList.add(Vt),this._queueCallback(()=>{this._element.classList.remove(Vt),this._queueCallback(()=>{this._element.style.overflowY=t},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){const e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._scrollBar.getWidth(),s=t>0;if(s&&!e){const c=x()?"paddingLeft":"paddingRight";this._element.style[c]=`${t}px`}if(!s&&e){const c=x()?"paddingRight":"paddingLeft";this._element.style[c]=`${t}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(e,t){return this.each(function(){const s=Se.getOrCreateInstance(this,e);if(typeof e=="string"){if(s[e]===void 0)throw new TypeError(`No method named "${e}"`);s[e](t)}})}}u.on(document,Ls,'[data-bs-toggle="modal"]',function(r){const e=v.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&r.preventDefault(),u.one(e,Ii,s=>{s.defaultPrevented||u.one(e,Pi,()=>{k(this)&&this.focus()})});const t=v.findOne(".modal.show");t&&Se.getInstance(t).hide(),Se.getOrCreateInstance(e).toggle(this)}),Re(Se),y(Se);const pe=".bs.offcanvas",Mi=".data-api",Is=`load${pe}${Mi}`,Fi="show",Bi="showing",Ri="hiding",Hi=".offcanvas.show",Ns=`show${pe}`,js=`shown${pe}`,Ms=`hide${pe}`,Wi=`hidePrevented${pe}`,zi=`hidden${pe}`,Fs=`resize${pe}`,Bs=`click${pe}${Mi}`,Rs=`keydown.dismiss${pe}`,Hs={backdrop:!0,keyboard:!0,scroll:!1},Ws={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class ge extends H{constructor(e,t){super(e,t),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return Hs}static get DefaultType(){return Ws}static get NAME(){return"offcanvas"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||u.trigger(this._element,Ns,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||new qt().hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(Bi),this._queueCallback(()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add(Fi),this._element.classList.remove(Bi),u.trigger(this._element,js,{relatedTarget:e})},this._element,!0))}hide(){this._isShown&&(u.trigger(this._element,Ms).defaultPrevented||(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(Ri),this._backdrop.hide(),this._queueCallback(()=>{this._element.classList.remove(Fi,Ri),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||new qt().reset(),u.trigger(this._element,zi)},this._element,!0)))}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const e=!!this._config.backdrop;return new xi({className:"offcanvas-backdrop",isVisible:e,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:e?()=>{this._config.backdrop!=="static"?this.hide():u.trigger(this._element,Wi)}:null})}_initializeFocusTrap(){return new $i({trapElement:this._element})}_addEventListeners(){u.on(this._element,Rs,e=>{e.key==="Escape"&&(this._config.keyboard?this.hide():u.trigger(this._element,Wi))})}static jQueryInterface(e){return this.each(function(){const t=ge.getOrCreateInstance(this,e);if(typeof e=="string"){if(t[e]===void 0||e.startsWith("_")||e==="constructor")throw new TypeError(`No method named "${e}"`);t[e](this)}})}}u.on(document,Bs,'[data-bs-toggle="offcanvas"]',function(r){const e=v.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&r.preventDefault(),w(this))return;u.one(e,zi,()=>{k(this)&&this.focus()});const t=v.findOne(Hi);t&&t!==e&&ge.getInstance(t).hide(),ge.getOrCreateInstance(e).toggle(this)}),u.on(window,Is,()=>{for(const r of v.find(Hi))ge.getOrCreateInstance(r).show()}),u.on(window,Fs,()=>{for(const r of v.find("[aria-modal][class*=show][class*=offcanvas-]"))getComputedStyle(r).position!=="fixed"&&ge.getOrCreateInstance(r).hide()}),Re(ge),y(ge);const qi={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},zs=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),qs=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,Vs=(r,e)=>{const t=r.nodeName.toLowerCase();return e.includes(t)?!zs.has(t)||!!qs.test(r.nodeValue):e.filter(s=>s instanceof RegExp).some(s=>s.test(t))},Xs={allowList:qi,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},Ys={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},Ks={entry:"(string|element|function|null)",selector:"(string|element)"};class Us extends K{constructor(e){super(),this._config=this._getConfig(e)}static get Default(){return Xs}static get DefaultType(){return Ys}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map(e=>this._resolvePossibleFunction(e)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(e){return this._checkContent(e),this._config.content={...this._config.content,...e},this}toHtml(){const e=document.createElement("div");e.innerHTML=this._maybeSanitize(this._config.template);for(const[c,p]of Object.entries(this._config.content))this._setContent(e,p,c);const t=e.children[0],s=this._resolvePossibleFunction(this._config.extraClass);return s&&t.classList.add(...s.split(" ")),t}_typeCheckConfig(e){super._typeCheckConfig(e),this._checkContent(e.content)}_checkContent(e){for(const[t,s]of Object.entries(e))super._typeCheckConfig({selector:t,entry:s},Ks)}_setContent(e,t,s){const c=v.findOne(s,e);c&&((t=this._resolvePossibleFunction(t))?g(t)?this._putElementInTemplate(E(t),c):this._config.html?c.innerHTML=this._maybeSanitize(t):c.textContent=t:c.remove())}_maybeSanitize(e){return this._config.sanitize?function(t,s,c){if(!t.length)return t;if(c&&typeof c=="function")return c(t);const p=new window.DOMParser().parseFromString(t,"text/html"),_=[].concat(...p.body.querySelectorAll("*"));for(const T of _){const I=T.nodeName.toLowerCase();if(!Object.keys(s).includes(I)){T.remove();continue}const Y=[].concat(...T.attributes),U=[].concat(s["*"]||[],s[I]||[]);for(const se of Y)Vs(se,U)||T.removeAttribute(se.nodeName)}return p.body.innerHTML}(e,this._config.allowList,this._config.sanitizeFn):e}_resolvePossibleFunction(e){return A(e,[void 0,this])}_putElementInTemplate(e,t){if(this._config.html)return t.innerHTML="",void t.append(e);t.textContent=e.textContent}}const Qs=new Set(["sanitize","allowList","sanitizeFn"]),Xt="fade",Tt="show",Gs=".tooltip-inner",Vi=".modal",Xi="hide.bs.modal",ct="hover",Yt="focus",Kt="click",Zs={AUTO:"auto",TOP:"top",RIGHT:x()?"left":"right",BOTTOM:"bottom",LEFT:x()?"right":"left"},Js={allowList:qi,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},er={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class Le extends H{constructor(e,t){if(a===void 0)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org/docs/v2/)");super(e,t),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return Js}static get DefaultType(){return er}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),u.off(this._element.closest(Vi),Xi,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if(this._element.style.display==="none")throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const e=u.trigger(this._element,this.constructor.eventName("show")),t=(L(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(e.defaultPrevented||!t)return;this._disposePopper();const s=this._getTipElement();this._element.setAttribute("aria-describedby",s.getAttribute("id"));const{container:c}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(c.append(s),u.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(s),s.classList.add(Tt),"ontouchstart"in document.documentElement)for(const p of[].concat(...document.body.children))u.on(p,"mouseover",O);this._queueCallback(()=>{u.trigger(this._element,this.constructor.eventName("shown")),this._isHovered===!1&&this._leave(),this._isHovered=!1},this.tip,this._isAnimated())}hide(){if(this._isShown()&&!u.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented){if(this._getTipElement().classList.remove(Tt),"ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))u.off(e,"mouseover",O);this._activeTrigger[Kt]=!1,this._activeTrigger[Yt]=!1,this._activeTrigger[ct]=!1,this._isHovered=null,this._queueCallback(()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),u.trigger(this._element,this.constructor.eventName("hidden")))},this.tip,this._isAnimated())}}update(){this._popper&&this._popper.update()}_isWithContent(){return!!this._getTitle()}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(e){const t=this._getTemplateFactory(e).toHtml();if(!t)return null;t.classList.remove(Xt,Tt),t.classList.add(`bs-${this.constructor.NAME}-auto`);const s=(c=>{do c+=Math.floor(1e6*Math.random());while(document.getElementById(c));return c})(this.constructor.NAME).toString();return t.setAttribute("id",s),this._isAnimated()&&t.classList.add(Xt),t}setContent(e){this._newContent=e,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(e){return this._templateFactory?this._templateFactory.changeContent(e):this._templateFactory=new Us({...this._config,content:e,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[Gs]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(e){return this.constructor.getOrCreateInstance(e.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(Xt)}_isShown(){return this.tip&&this.tip.classList.contains(Tt)}_createPopper(e){const t=A(this._config.placement,[this,e,this._element]),s=Zs[t.toUpperCase()];return a.createPopper(this._element,e,this._getPopperConfig(s))}_getOffset(){const{offset:e}=this._config;return typeof e=="string"?e.split(",").map(t=>Number.parseInt(t,10)):typeof e=="function"?t=>e(t,this._element):e}_resolvePossibleFunction(e){return A(e,[this._element,this._element])}_getPopperConfig(e){const t={placement:e,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:s=>{this._getTipElement().setAttribute("data-popper-placement",s.state.placement)}}]};return{...t,...A(this._config.popperConfig,[void 0,t])}}_setListeners(){const e=this._config.trigger.split(" ");for(const t of e)if(t==="click")u.on(this._element,this.constructor.eventName("click"),this._config.selector,s=>{const c=this._initializeOnDelegatedTarget(s);c._activeTrigger[Kt]=!(c._isShown()&&c._activeTrigger[Kt]),c.toggle()});else if(t!=="manual"){const s=t===ct?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),c=t===ct?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");u.on(this._element,s,this._config.selector,p=>{const _=this._initializeOnDelegatedTarget(p);_._activeTrigger[p.type==="focusin"?Yt:ct]=!0,_._enter()}),u.on(this._element,c,this._config.selector,p=>{const _=this._initializeOnDelegatedTarget(p);_._activeTrigger[p.type==="focusout"?Yt:ct]=_._element.contains(p.relatedTarget),_._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},u.on(this._element.closest(Vi),Xi,this._hideModalHandler)}_fixTitle(){const e=this._element.getAttribute("title");e&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",e),this._element.setAttribute("data-bs-original-title",e),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(e,t){clearTimeout(this._timeout),this._timeout=setTimeout(e,t)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(e){const t=X.getDataAttributes(this._element);for(const s of Object.keys(t))Qs.has(s)&&delete t[s];return e={...t,...typeof e=="object"&&e?e:{}},e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e.container=e.container===!1?document.body:E(e.container),typeof e.delay=="number"&&(e.delay={show:e.delay,hide:e.delay}),typeof e.title=="number"&&(e.title=e.title.toString()),typeof e.content=="number"&&(e.content=e.content.toString()),e}_getDelegateConfig(){const e={};for(const[t,s]of Object.entries(this._config))this.constructor.Default[t]!==s&&(e[t]=s);return e.selector=!1,e.trigger="manual",e}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(e){return this.each(function(){const t=Le.getOrCreateInstance(this,e);if(typeof e=="string"){if(t[e]===void 0)throw new TypeError(`No method named "${e}"`);t[e]()}})}}y(Le);const tr=".popover-header",ir=".popover-body",nr={...Le.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},sr={...Le.DefaultType,content:"(null|string|element|function)"};class xt extends Le{static get Default(){return nr}static get DefaultType(){return sr}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[tr]:this._getTitle(),[ir]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(e){return this.each(function(){const t=xt.getOrCreateInstance(this,e);if(typeof e=="string"){if(t[e]===void 0)throw new TypeError(`No method named "${e}"`);t[e]()}})}}y(xt);const Ut=".bs.scrollspy",rr=`activate${Ut}`,Yi=`click${Ut}`,or=`load${Ut}.data-api`,Ke="active",Qt="[href]",Ki=".nav-link",ar=`${Ki}, .nav-item > ${Ki}, .list-group-item`,lr={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},cr={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class ut extends H{constructor(e,t){super(e,t),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement=getComputedStyle(this._element).overflowY==="visible"?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return lr}static get DefaultType(){return cr}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const e of this._observableSections.values())this._observer.observe(e)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(e){return e.target=E(e.target)||document.body,e.rootMargin=e.offset?`${e.offset}px 0px -30%`:e.rootMargin,typeof e.threshold=="string"&&(e.threshold=e.threshold.split(",").map(t=>Number.parseFloat(t))),e}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(u.off(this._config.target,Yi),u.on(this._config.target,Yi,Qt,e=>{const t=this._observableSections.get(e.target.hash);if(t){e.preventDefault();const s=this._rootElement||window,c=t.offsetTop-this._element.offsetTop;if(s.scrollTo)return void s.scrollTo({top:c,behavior:"smooth"});s.scrollTop=c}}))}_getNewObserver(){const e={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(t=>this._observerCallback(t),e)}_observerCallback(e){const t=_=>this._targetLinks.get(`#${_.target.id}`),s=_=>{this._previousScrollData.visibleEntryTop=_.target.offsetTop,this._process(t(_))},c=(this._rootElement||document.documentElement).scrollTop,p=c>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=c;for(const _ of e){if(!_.isIntersecting){this._activeTarget=null,this._clearActiveClass(t(_));continue}const T=_.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(p&&T){if(s(_),!c)return}else p||T||s(_)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const e=v.find(Qt,this._config.target);for(const t of e){if(!t.hash||w(t))continue;const s=v.findOne(decodeURI(t.hash),this._element);k(s)&&(this._targetLinks.set(decodeURI(t.hash),t),this._observableSections.set(t.hash,s))}}_process(e){this._activeTarget!==e&&(this._clearActiveClass(this._config.target),this._activeTarget=e,e.classList.add(Ke),this._activateParents(e),u.trigger(this._element,rr,{relatedTarget:e}))}_activateParents(e){if(e.classList.contains("dropdown-item"))v.findOne(".dropdown-toggle",e.closest(".dropdown")).classList.add(Ke);else for(const t of v.parents(e,".nav, .list-group"))for(const s of v.prev(t,ar))s.classList.add(Ke)}_clearActiveClass(e){e.classList.remove(Ke);const t=v.find(`${Qt}.${Ke}`,e);for(const s of t)s.classList.remove(Ke)}static jQueryInterface(e){return this.each(function(){const t=ut.getOrCreateInstance(this,e);if(typeof e=="string"){if(t[e]===void 0||e.startsWith("_")||e==="constructor")throw new TypeError(`No method named "${e}"`);t[e]()}})}}u.on(window,or,()=>{for(const r of v.find('[data-bs-spy="scroll"]'))ut.getOrCreateInstance(r)}),y(ut);const De=".bs.tab",ur=`hide${De}`,hr=`hidden${De}`,dr=`show${De}`,fr=`shown${De}`,pr=`click${De}`,gr=`keydown${De}`,mr=`load${De}`,_r="ArrowLeft",Ui="ArrowRight",vr="ArrowUp",Qi="ArrowDown",Gt="Home",Gi="End",Pe="active",Zi="fade",Zt="show",Ji=".dropdown-toggle",Jt=`:not(${Ji})`,en='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',ei=`.nav-link${Jt}, .list-group-item${Jt}, [role="tab"]${Jt}, ${en}`,br=`.${Pe}[data-bs-toggle="tab"], .${Pe}[data-bs-toggle="pill"], .${Pe}[data-bs-toggle="list"]`;class Ie extends H{constructor(e){super(e),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),u.on(this._element,gr,t=>this._keydown(t)))}static get NAME(){return"tab"}show(){const e=this._element;if(this._elemIsActive(e))return;const t=this._getActiveElem(),s=t?u.trigger(t,ur,{relatedTarget:e}):null;u.trigger(e,dr,{relatedTarget:t}).defaultPrevented||s&&s.defaultPrevented||(this._deactivate(t,e),this._activate(e,t))}_activate(e,t){e&&(e.classList.add(Pe),this._activate(v.getElementFromSelector(e)),this._queueCallback(()=>{e.getAttribute("role")==="tab"?(e.removeAttribute("tabindex"),e.setAttribute("aria-selected",!0),this._toggleDropDown(e,!0),u.trigger(e,fr,{relatedTarget:t})):e.classList.add(Zt)},e,e.classList.contains(Zi)))}_deactivate(e,t){e&&(e.classList.remove(Pe),e.blur(),this._deactivate(v.getElementFromSelector(e)),this._queueCallback(()=>{e.getAttribute("role")==="tab"?(e.setAttribute("aria-selected",!1),e.setAttribute("tabindex","-1"),this._toggleDropDown(e,!1),u.trigger(e,hr,{relatedTarget:t})):e.classList.remove(Zt)},e,e.classList.contains(Zi)))}_keydown(e){if(![_r,Ui,vr,Qi,Gt,Gi].includes(e.key))return;e.stopPropagation(),e.preventDefault();const t=this._getChildren().filter(c=>!w(c));let s;if([Gt,Gi].includes(e.key))s=t[e.key===Gt?0:t.length-1];else{const c=[Ui,Qi].includes(e.key);s=D(t,e.target,c,!0)}s&&(s.focus({preventScroll:!0}),Ie.getOrCreateInstance(s).show())}_getChildren(){return v.find(ei,this._parent)}_getActiveElem(){return this._getChildren().find(e=>this._elemIsActive(e))||null}_setInitialAttributes(e,t){this._setAttributeIfNotExists(e,"role","tablist");for(const s of t)this._setInitialAttributesOnChild(s)}_setInitialAttributesOnChild(e){e=this._getInnerElement(e);const t=this._elemIsActive(e),s=this._getOuterElement(e);e.setAttribute("aria-selected",t),s!==e&&this._setAttributeIfNotExists(s,"role","presentation"),t||e.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(e,"role","tab"),this._setInitialAttributesOnTargetPanel(e)}_setInitialAttributesOnTargetPanel(e){const t=v.getElementFromSelector(e);t&&(this._setAttributeIfNotExists(t,"role","tabpanel"),e.id&&this._setAttributeIfNotExists(t,"aria-labelledby",`${e.id}`))}_toggleDropDown(e,t){const s=this._getOuterElement(e);if(!s.classList.contains("dropdown"))return;const c=(p,_)=>{const T=v.findOne(p,s);T&&T.classList.toggle(_,t)};c(Ji,Pe),c(".dropdown-menu",Zt),s.setAttribute("aria-expanded",t)}_setAttributeIfNotExists(e,t,s){e.hasAttribute(t)||e.setAttribute(t,s)}_elemIsActive(e){return e.classList.contains(Pe)}_getInnerElement(e){return e.matches(ei)?e:v.findOne(ei,e)}_getOuterElement(e){return e.closest(".nav-item, .list-group-item")||e}static jQueryInterface(e){return this.each(function(){const t=Ie.getOrCreateInstance(this);if(typeof e=="string"){if(t[e]===void 0||e.startsWith("_")||e==="constructor")throw new TypeError(`No method named "${e}"`);t[e]()}})}}u.on(document,pr,en,function(r){["A","AREA"].includes(this.tagName)&&r.preventDefault(),w(this)||Ie.getOrCreateInstance(this).show()}),u.on(window,mr,()=>{for(const r of v.find(br))Ie.getOrCreateInstance(r)}),y(Ie);const Ee=".bs.toast",yr=`mouseover${Ee}`,wr=`mouseout${Ee}`,Ar=`focusin${Ee}`,Er=`focusout${Ee}`,Cr=`hide${Ee}`,Or=`hidden${Ee}`,Tr=`show${Ee}`,xr=`shown${Ee}`,tn="hide",kt="show",$t="showing",kr={animation:"boolean",autohide:"boolean",delay:"number"},$r={animation:!0,autohide:!0,delay:5e3};class ht extends H{constructor(e,t){super(e,t),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return $r}static get DefaultType(){return kr}static get NAME(){return"toast"}show(){u.trigger(this._element,Tr).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove(tn),C(this._element),this._element.classList.add(kt,$t),this._queueCallback(()=>{this._element.classList.remove($t),u.trigger(this._element,xr),this._maybeScheduleHide()},this._element,this._config.animation))}hide(){this.isShown()&&(u.trigger(this._element,Cr).defaultPrevented||(this._element.classList.add($t),this._queueCallback(()=>{this._element.classList.add(tn),this._element.classList.remove($t,kt),u.trigger(this._element,Or)},this._element,this._config.animation)))}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(kt),super.dispose()}isShown(){return this._element.classList.contains(kt)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(e,t){switch(e.type){case"mouseover":case"mouseout":this._hasMouseInteraction=t;break;case"focusin":case"focusout":this._hasKeyboardInteraction=t}if(t)return void this._clearTimeout();const s=e.relatedTarget;this._element===s||this._element.contains(s)||this._maybeScheduleHide()}_setListeners(){u.on(this._element,yr,e=>this._onInteraction(e,!0)),u.on(this._element,wr,e=>this._onInteraction(e,!1)),u.on(this._element,Ar,e=>this._onInteraction(e,!0)),u.on(this._element,Er,e=>this._onInteraction(e,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(e){return this.each(function(){const t=ht.getOrCreateInstance(this,e);if(typeof e=="string"){if(t[e]===void 0)throw new TypeError(`No method named "${e}"`);t[e](this)}})}}return Re(ht),y(ht),{Alert:we,Button:Te,Carousel:qe,Collapse:Xe,Dropdown:ne,Modal:Se,Offcanvas:ge,Popover:xt,ScrollSpy:ut,Tab:Ie,Toast:ht,Tooltip:Le}})}(Dt)),Dt.exports}Co();
