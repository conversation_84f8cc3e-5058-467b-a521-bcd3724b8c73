var tt={exports:{}};/*!
 * Tabler v1.4.0 (https://tabler.io)
 * Copyright 2018-2025 The Tabler Authors
 * Copyright 2018-2025 codecalm.net Paweł Kuna
 * Licensed under MIT (https://github.com/tabler/tabler/blob/master/LICENSE)
 */var zr=tt.exports,cs;function qr(){return cs||(cs=1,function(Wr,us){(function(D,it){it(us)})(zr,function(D){const it=document.querySelectorAll('[data-bs-toggle="autosize"]');it.length&&it.forEach(function(i){window.autosize&&window.autosize(i)});const fi=document.querySelectorAll("[data-countup]");fi.length&&fi.forEach(function(i){let e={};try{const n=i.getAttribute("data-countup")?JSON.parse(i.getAttribute("data-countup")):{};e=Object.assign({enableScrollSpy:!0},n)}catch{}const t=parseInt(i.innerHTML,10);if(window.countUp&&window.countUp.CountUp){const n=new window.countUp.CountUp(i,t,e);n.error||n.start()}}),[].slice.call(document.querySelectorAll("[data-mask]")).map(function(i){window.IMask&&new window.IMask(i,{mask:i.dataset.mask,lazy:i.dataset["mask-visible"]==="true"})});var I="top",F="bottom",H="right",P="left",nt="auto",ke=[I,F,H,P],_e="start",Se="end",pi="clippingParents",Tt="viewport",Le="popper",mi="reference",Ct=ke.reduce(function(i,e){return i.concat([e+"-"+_e,e+"-"+Se])},[]),Ot=[].concat(ke,[nt]).reduce(function(i,e){return i.concat([e,e+"-"+_e,e+"-"+Se])},[]),gi="beforeRead",_i="read",bi="afterRead",vi="beforeMain",yi="main",wi="afterMain",Ai="beforeWrite",Ei="write",Ti="afterWrite",Ci=[gi,_i,bi,vi,yi,wi,Ai,Ei,Ti];function G(i){return i?(i.nodeName||"").toLowerCase():null}function z(i){if(i==null)return window;if(i.toString()!=="[object Window]"){var e=i.ownerDocument;return e&&e.defaultView||window}return i}function be(i){return i instanceof z(i).Element||i instanceof Element}function R(i){return i instanceof z(i).HTMLElement||i instanceof HTMLElement}function xt(i){return typeof ShadowRoot<"u"&&(i instanceof z(i).ShadowRoot||i instanceof ShadowRoot)}const kt={name:"applyStyles",enabled:!0,phase:"write",fn:function(i){var e=i.state;Object.keys(e.elements).forEach(function(t){var n=e.styles[t]||{},s=e.attributes[t]||{},o=e.elements[t];R(o)&&G(o)&&(Object.assign(o.style,n),Object.keys(s).forEach(function(r){var a=s[r];a===!1?o.removeAttribute(r):o.setAttribute(r,a===!0?"":a)}))})},effect:function(i){var e=i.state,t={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,t.popper),e.styles=t,e.elements.arrow&&Object.assign(e.elements.arrow.style,t.arrow),function(){Object.keys(e.elements).forEach(function(n){var s=e.elements[n],o=e.attributes[n]||{},r=Object.keys(e.styles.hasOwnProperty(n)?e.styles[n]:t[n]).reduce(function(a,c){return a[c]="",a},{});R(s)&&G(s)&&(Object.assign(s.style,r),Object.keys(o).forEach(function(a){s.removeAttribute(a)}))})}},requires:["computeStyles"]};function Z(i){return i.split("-")[0]}var ve=Math.max,st=Math.min,$e=Math.round;function St(){var i=navigator.userAgentData;return i!=null&&i.brands&&Array.isArray(i.brands)?i.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function Oi(){return!/^((?!chrome|android).)*safari/i.test(St())}function De(i,e,t){e===void 0&&(e=!1),t===void 0&&(t=!1);var n=i.getBoundingClientRect(),s=1,o=1;e&&R(i)&&(s=i.offsetWidth>0&&$e(n.width)/i.offsetWidth||1,o=i.offsetHeight>0&&$e(n.height)/i.offsetHeight||1);var r=(be(i)?z(i):window).visualViewport,a=!Oi()&&t,c=(n.left+(a&&r?r.offsetLeft:0))/s,h=(n.top+(a&&r?r.offsetTop:0))/o,d=n.width/s,u=n.height/o;return{width:d,height:u,top:h,right:c+d,bottom:h+u,left:c,x:c,y:h}}function Lt(i){var e=De(i),t=i.offsetWidth,n=i.offsetHeight;return Math.abs(e.width-t)<=1&&(t=e.width),Math.abs(e.height-n)<=1&&(n=e.height),{x:i.offsetLeft,y:i.offsetTop,width:t,height:n}}function xi(i,e){var t=e.getRootNode&&e.getRootNode();if(i.contains(e))return!0;if(t&&xt(t)){var n=e;do{if(n&&i.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function ne(i){return z(i).getComputedStyle(i)}function hs(i){return["table","td","th"].indexOf(G(i))>=0}function ce(i){return((be(i)?i.ownerDocument:i.document)||window.document).documentElement}function ot(i){return G(i)==="html"?i:i.assignedSlot||i.parentNode||(xt(i)?i.host:null)||ce(i)}function ki(i){return R(i)&&ne(i).position!=="fixed"?i.offsetParent:null}function Ke(i){for(var e=z(i),t=ki(i);t&&hs(t)&&ne(t).position==="static";)t=ki(t);return t&&(G(t)==="html"||G(t)==="body"&&ne(t).position==="static")?e:t||function(n){var s=/firefox/i.test(St());if(/Trident/i.test(St())&&R(n)&&ne(n).position==="fixed")return null;var o=ot(n);for(xt(o)&&(o=o.host);R(o)&&["html","body"].indexOf(G(o))<0;){var r=ne(o);if(r.transform!=="none"||r.perspective!=="none"||r.contain==="paint"||["transform","perspective"].indexOf(r.willChange)!==-1||s&&r.willChange==="filter"||s&&r.filter&&r.filter!=="none")return o;o=o.parentNode}return null}(i)||e}function $t(i){return["top","bottom"].indexOf(i)>=0?"x":"y"}function Qe(i,e,t){return ve(i,st(e,t))}function Si(i){return Object.assign({},{top:0,right:0,bottom:0,left:0},i)}function Li(i,e){return e.reduce(function(t,n){return t[n]=i,t},{})}const $i={name:"arrow",enabled:!0,phase:"main",fn:function(i){var e,t=i.state,n=i.name,s=i.options,o=t.elements.arrow,r=t.modifiersData.popperOffsets,a=Z(t.placement),c=$t(a),h=[P,H].indexOf(a)>=0?"height":"width";if(o&&r){var d=function(O,T){return Si(typeof(O=typeof O=="function"?O(Object.assign({},T.rects,{placement:T.placement})):O)!="number"?O:Li(O,ke))}(s.padding,t),u=Lt(o),b=c==="y"?I:P,p=c==="y"?F:H,g=t.rects.reference[h]+t.rects.reference[c]-r[c]-t.rects.popper[h],m=r[c]-t.rects.reference[c],_=Ke(o),C=_?c==="y"?_.clientHeight||0:_.clientWidth||0:0,k=g/2-m/2,y=d[b],A=C-u[h]-d[p],v=C/2-u[h]/2+k,w=Qe(y,v,A),E=c;t.modifiersData[n]=((e={})[E]=w,e.centerOffset=w-v,e)}},effect:function(i){var e=i.state,t=i.options.element,n=t===void 0?"[data-popper-arrow]":t;n!=null&&(typeof n!="string"||(n=e.elements.popper.querySelector(n)))&&xi(e.elements.popper,n)&&(e.elements.arrow=n)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Ie(i){return i.split("-")[1]}var ds={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Di(i){var e,t=i.popper,n=i.popperRect,s=i.placement,o=i.variation,r=i.offsets,a=i.position,c=i.gpuAcceleration,h=i.adaptive,d=i.roundOffsets,u=i.isFixed,b=r.x,p=b===void 0?0:b,g=r.y,m=g===void 0?0:g,_=typeof d=="function"?d({x:p,y:m}):{x:p,y:m};p=_.x,m=_.y;var C=r.hasOwnProperty("x"),k=r.hasOwnProperty("y"),y=P,A=I,v=window;if(h){var w=Ke(t),E="clientHeight",O="clientWidth";w===z(t)&&ne(w=ce(t)).position!=="static"&&a==="absolute"&&(E="scrollHeight",O="scrollWidth"),(s===I||(s===P||s===H)&&o===Se)&&(A=F,m-=(u&&w===v&&v.visualViewport?v.visualViewport.height:w[E])-n.height,m*=c?1:-1),s!==P&&(s!==I&&s!==F||o!==Se)||(y=H,p-=(u&&w===v&&v.visualViewport?v.visualViewport.width:w[O])-n.width,p*=c?1:-1)}var T,L=Object.assign({position:a},h&&ds),W=d===!0?function(J,N){var K=J.x,Q=J.y,S=N.devicePixelRatio||1;return{x:$e(K*S)/S||0,y:$e(Q*S)/S||0}}({x:p,y:m},z(t)):{x:p,y:m};return p=W.x,m=W.y,c?Object.assign({},L,((T={})[A]=k?"0":"",T[y]=C?"0":"",T.transform=(v.devicePixelRatio||1)<=1?"translate("+p+"px, "+m+"px)":"translate3d("+p+"px, "+m+"px, 0)",T)):Object.assign({},L,((e={})[A]=k?m+"px":"",e[y]=C?p+"px":"",e.transform="",e))}const Dt={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(i){var e=i.state,t=i.options,n=t.gpuAcceleration,s=n===void 0||n,o=t.adaptive,r=o===void 0||o,a=t.roundOffsets,c=a===void 0||a,h={placement:Z(e.placement),variation:Ie(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:s,isFixed:e.options.strategy==="fixed"};e.modifiersData.popperOffsets!=null&&(e.styles.popper=Object.assign({},e.styles.popper,Di(Object.assign({},h,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:r,roundOffsets:c})))),e.modifiersData.arrow!=null&&(e.styles.arrow=Object.assign({},e.styles.arrow,Di(Object.assign({},h,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:c})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})},data:{}};var rt={passive:!0};const It={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(i){var e=i.state,t=i.instance,n=i.options,s=n.scroll,o=s===void 0||s,r=n.resize,a=r===void 0||r,c=z(e.elements.popper),h=[].concat(e.scrollParents.reference,e.scrollParents.popper);return o&&h.forEach(function(d){d.addEventListener("scroll",t.update,rt)}),a&&c.addEventListener("resize",t.update,rt),function(){o&&h.forEach(function(d){d.removeEventListener("scroll",t.update,rt)}),a&&c.removeEventListener("resize",t.update,rt)}},data:{}};var fs={left:"right",right:"left",bottom:"top",top:"bottom"};function at(i){return i.replace(/left|right|bottom|top/g,function(e){return fs[e]})}var ps={start:"end",end:"start"};function Ii(i){return i.replace(/start|end/g,function(e){return ps[e]})}function Pt(i){var e=z(i);return{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function Nt(i){return De(ce(i)).left+Pt(i).scrollLeft}function Mt(i){var e=ne(i),t=e.overflow,n=e.overflowX,s=e.overflowY;return/auto|scroll|overlay|hidden/.test(t+s+n)}function Pi(i){return["html","body","#document"].indexOf(G(i))>=0?i.ownerDocument.body:R(i)&&Mt(i)?i:Pi(ot(i))}function Xe(i,e){var t;e===void 0&&(e=[]);var n=Pi(i),s=n===((t=i.ownerDocument)==null?void 0:t.body),o=z(n),r=s?[o].concat(o.visualViewport||[],Mt(n)?n:[]):n,a=e.concat(r);return s?a:a.concat(Xe(ot(r)))}function jt(i){return Object.assign({},i,{left:i.x,top:i.y,right:i.x+i.width,bottom:i.y+i.height})}function Ni(i,e,t){return e===Tt?jt(function(n,s){var o=z(n),r=ce(n),a=o.visualViewport,c=r.clientWidth,h=r.clientHeight,d=0,u=0;if(a){c=a.width,h=a.height;var b=Oi();(b||!b&&s==="fixed")&&(d=a.offsetLeft,u=a.offsetTop)}return{width:c,height:h,x:d+Nt(n),y:u}}(i,t)):be(e)?function(n,s){var o=De(n,!1,s==="fixed");return o.top=o.top+n.clientTop,o.left=o.left+n.clientLeft,o.bottom=o.top+n.clientHeight,o.right=o.left+n.clientWidth,o.width=n.clientWidth,o.height=n.clientHeight,o.x=o.left,o.y=o.top,o}(e,t):jt(function(n){var s,o=ce(n),r=Pt(n),a=(s=n.ownerDocument)==null?void 0:s.body,c=ve(o.scrollWidth,o.clientWidth,a?a.scrollWidth:0,a?a.clientWidth:0),h=ve(o.scrollHeight,o.clientHeight,a?a.scrollHeight:0,a?a.clientHeight:0),d=-r.scrollLeft+Nt(n),u=-r.scrollTop;return ne(a||o).direction==="rtl"&&(d+=ve(o.clientWidth,a?a.clientWidth:0)-c),{width:c,height:h,x:d,y:u}}(ce(i)))}function Mi(i){var e,t=i.reference,n=i.element,s=i.placement,o=s?Z(s):null,r=s?Ie(s):null,a=t.x+t.width/2-n.width/2,c=t.y+t.height/2-n.height/2;switch(o){case I:e={x:a,y:t.y-n.height};break;case F:e={x:a,y:t.y+t.height};break;case H:e={x:t.x+t.width,y:c};break;case P:e={x:t.x-n.width,y:c};break;default:e={x:t.x,y:t.y}}var h=o?$t(o):null;if(h!=null){var d=h==="y"?"height":"width";switch(r){case _e:e[h]=e[h]-(t[d]/2-n[d]/2);break;case Se:e[h]=e[h]+(t[d]/2-n[d]/2)}}return e}function Pe(i,e){e===void 0&&(e={});var t=e,n=t.placement,s=n===void 0?i.placement:n,o=t.strategy,r=o===void 0?i.strategy:o,a=t.boundary,c=a===void 0?pi:a,h=t.rootBoundary,d=h===void 0?Tt:h,u=t.elementContext,b=u===void 0?Le:u,p=t.altBoundary,g=p!==void 0&&p,m=t.padding,_=m===void 0?0:m,C=Si(typeof _!="number"?_:Li(_,ke)),k=b===Le?mi:Le,y=i.rects.popper,A=i.elements[g?k:b],v=function(N,K,Q,S){var te=K==="clippingParents"?function(x){var M=Xe(ot(x)),X=["absolute","fixed"].indexOf(ne(x).position)>=0&&R(x)?Ke(x):x;return be(X)?M.filter(function(ge){return be(ge)&&xi(ge,X)&&G(ge)!=="body"}):[]}(N):[].concat(K),ie=[].concat(te,[Q]),Ue=ie[0],$=ie.reduce(function(x,M){var X=Ni(N,M,S);return x.top=ve(X.top,x.top),x.right=st(X.right,x.right),x.bottom=st(X.bottom,x.bottom),x.left=ve(X.left,x.left),x},Ni(N,Ue,S));return $.width=$.right-$.left,$.height=$.bottom-$.top,$.x=$.left,$.y=$.top,$}(be(A)?A:A.contextElement||ce(i.elements.popper),c,d,r),w=De(i.elements.reference),E=Mi({reference:w,element:y,placement:s}),O=jt(Object.assign({},y,E)),T=b===Le?O:w,L={top:v.top-T.top+C.top,bottom:T.bottom-v.bottom+C.bottom,left:v.left-T.left+C.left,right:T.right-v.right+C.right},W=i.modifiersData.offset;if(b===Le&&W){var J=W[s];Object.keys(L).forEach(function(N){var K=[H,F].indexOf(N)>=0?1:-1,Q=[I,F].indexOf(N)>=0?"y":"x";L[N]+=J[Q]*K})}return L}function ms(i,e){e===void 0&&(e={});var t=e,n=t.placement,s=t.boundary,o=t.rootBoundary,r=t.padding,a=t.flipVariations,c=t.allowedAutoPlacements,h=c===void 0?Ot:c,d=Ie(n),u=d?a?Ct:Ct.filter(function(g){return Ie(g)===d}):ke,b=u.filter(function(g){return h.indexOf(g)>=0});b.length===0&&(b=u);var p=b.reduce(function(g,m){return g[m]=Pe(i,{placement:m,boundary:s,rootBoundary:o,padding:r})[Z(m)],g},{});return Object.keys(p).sort(function(g,m){return p[g]-p[m]})}const ji={name:"flip",enabled:!0,phase:"main",fn:function(i){var e=i.state,t=i.options,n=i.name;if(!e.modifiersData[n]._skip){for(var s=t.mainAxis,o=s===void 0||s,r=t.altAxis,a=r===void 0||r,c=t.fallbackPlacements,h=t.padding,d=t.boundary,u=t.rootBoundary,b=t.altBoundary,p=t.flipVariations,g=p===void 0||p,m=t.allowedAutoPlacements,_=e.options.placement,C=Z(_),k=c||(C!==_&&g?function(x){if(Z(x)===nt)return[];var M=at(x);return[Ii(x),M,Ii(M)]}(_):[at(_)]),y=[_].concat(k).reduce(function(x,M){return x.concat(Z(M)===nt?ms(e,{placement:M,boundary:d,rootBoundary:u,padding:h,flipVariations:g,allowedAutoPlacements:m}):M)},[]),A=e.rects.reference,v=e.rects.popper,w=new Map,E=!0,O=y[0],T=0;T<y.length;T++){var L=y[T],W=Z(L),J=Ie(L)===_e,N=[I,F].indexOf(W)>=0,K=N?"width":"height",Q=Pe(e,{placement:L,boundary:d,rootBoundary:u,altBoundary:b,padding:h}),S=N?J?H:P:J?F:I;A[K]>v[K]&&(S=at(S));var te=at(S),ie=[];if(o&&ie.push(Q[W]<=0),a&&ie.push(Q[S]<=0,Q[te]<=0),ie.every(function(x){return x})){O=L,E=!1;break}w.set(L,ie)}if(E)for(var Ue=function(x){var M=y.find(function(X){var ge=w.get(X);if(ge)return ge.slice(0,x).every(function(wt){return wt})});if(M)return O=M,"break"},$=g?3:1;$>0&&Ue($)!=="break";$--);e.placement!==O&&(e.modifiersData[n]._skip=!0,e.placement=O,e.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function Fi(i,e,t){return t===void 0&&(t={x:0,y:0}),{top:i.top-e.height-t.y,right:i.right-e.width+t.x,bottom:i.bottom-e.height+t.y,left:i.left-e.width-t.x}}function Hi(i){return[I,H,F,P].some(function(e){return i[e]>=0})}const zi={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(i){var e=i.state,t=i.name,n=e.rects.reference,s=e.rects.popper,o=e.modifiersData.preventOverflow,r=Pe(e,{elementContext:"reference"}),a=Pe(e,{altBoundary:!0}),c=Fi(r,n),h=Fi(a,s,o),d=Hi(c),u=Hi(h);e.modifiersData[t]={referenceClippingOffsets:c,popperEscapeOffsets:h,isReferenceHidden:d,hasPopperEscaped:u},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":d,"data-popper-escaped":u})}},qi={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(i){var e=i.state,t=i.options,n=i.name,s=t.offset,o=s===void 0?[0,0]:s,r=Ot.reduce(function(d,u){return d[u]=function(b,p,g){var m=Z(b),_=[P,I].indexOf(m)>=0?-1:1,C=typeof g=="function"?g(Object.assign({},p,{placement:b})):g,k=C[0],y=C[1];return k=k||0,y=(y||0)*_,[P,H].indexOf(m)>=0?{x:y,y:k}:{x:k,y}}(u,e.rects,o),d},{}),a=r[e.placement],c=a.x,h=a.y;e.modifiersData.popperOffsets!=null&&(e.modifiersData.popperOffsets.x+=c,e.modifiersData.popperOffsets.y+=h),e.modifiersData[n]=r}},Ft={name:"popperOffsets",enabled:!0,phase:"read",fn:function(i){var e=i.state,t=i.name;e.modifiersData[t]=Mi({reference:e.rects.reference,element:e.rects.popper,placement:e.placement})},data:{}},Wi={name:"preventOverflow",enabled:!0,phase:"main",fn:function(i){var e=i.state,t=i.options,n=i.name,s=t.mainAxis,o=s===void 0||s,r=t.altAxis,a=r!==void 0&&r,c=t.boundary,h=t.rootBoundary,d=t.altBoundary,u=t.padding,b=t.tether,p=b===void 0||b,g=t.tetherOffset,m=g===void 0?0:g,_=Pe(e,{boundary:c,rootBoundary:h,padding:u,altBoundary:d}),C=Z(e.placement),k=Ie(e.placement),y=!k,A=$t(C),v=A==="x"?"y":"x",w=e.modifiersData.popperOffsets,E=e.rects.reference,O=e.rects.popper,T=typeof m=="function"?m(Object.assign({},e.rects,{placement:e.placement})):m,L=typeof T=="number"?{mainAxis:T,altAxis:T}:Object.assign({mainAxis:0,altAxis:0},T),W=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,J={x:0,y:0};if(w){if(o){var N,K=A==="y"?I:P,Q=A==="y"?F:H,S=A==="y"?"height":"width",te=w[A],ie=te+_[K],Ue=te-_[Q],$=p?-O[S]/2:0,x=k===_e?E[S]:O[S],M=k===_e?-O[S]:-E[S],X=e.elements.arrow,ge=p&&X?Lt(X):{width:0,height:0},wt=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},Jn=wt[K],Gn=wt[Q],At=Qe(0,E[S],ge[S]),Dr=y?E[S]/2-$-At-Jn-L.mainAxis:x-At-Jn-L.mainAxis,Ir=y?-E[S]/2+$+At+Gn+L.mainAxis:M+At+Gn+L.mainAxis,ui=e.elements.arrow&&Ke(e.elements.arrow),Pr=ui?A==="y"?ui.clientTop||0:ui.clientLeft||0:0,Zn=(N=W?.[A])!=null?N:0,Nr=te+Ir-Zn,es=Qe(p?st(ie,te+Dr-Zn-Pr):ie,te,p?ve(Ue,Nr):Ue);w[A]=es,J[A]=es-te}if(a){var ts,Mr=A==="x"?I:P,jr=A==="x"?F:H,xe=w[v],Et=v==="y"?"height":"width",is=xe+_[Mr],ns=xe-_[jr],hi=[I,P].indexOf(C)!==-1,ss=(ts=W?.[v])!=null?ts:0,os=hi?is:xe-E[Et]-O[Et]-ss+L.altAxis,rs=hi?xe+E[Et]+O[Et]-ss-L.altAxis:ns,as=p&&hi?function(Fr,Hr,di){var ls=Qe(Fr,Hr,di);return ls>di?di:ls}(os,xe,rs):Qe(p?os:is,xe,p?rs:ns);w[v]=as,J[v]=as-xe}e.modifiersData[n]=J}},requiresIfExists:["offset"]};function gs(i,e,t){t===void 0&&(t=!1);var n,s,o=R(e),r=R(e)&&function(u){var b=u.getBoundingClientRect(),p=$e(b.width)/u.offsetWidth||1,g=$e(b.height)/u.offsetHeight||1;return p!==1||g!==1}(e),a=ce(e),c=De(i,r,t),h={scrollLeft:0,scrollTop:0},d={x:0,y:0};return(o||!o&&!t)&&((G(e)!=="body"||Mt(a))&&(h=(n=e)!==z(n)&&R(n)?{scrollLeft:(s=n).scrollLeft,scrollTop:s.scrollTop}:Pt(n)),R(e)?((d=De(e,!0)).x+=e.clientLeft,d.y+=e.clientTop):a&&(d.x=Nt(a))),{x:c.left+h.scrollLeft-d.x,y:c.top+h.scrollTop-d.y,width:c.width,height:c.height}}function _s(i){var e=new Map,t=new Set,n=[];function s(o){t.add(o.name),[].concat(o.requires||[],o.requiresIfExists||[]).forEach(function(r){if(!t.has(r)){var a=e.get(r);a&&s(a)}}),n.push(o)}return i.forEach(function(o){e.set(o.name,o)}),i.forEach(function(o){t.has(o.name)||s(o)}),n}var Ri={placement:"bottom",modifiers:[],strategy:"absolute"};function Bi(){for(var i=arguments.length,e=new Array(i),t=0;t<i;t++)e[t]=arguments[t];return!e.some(function(n){return!(n&&typeof n.getBoundingClientRect=="function")})}function lt(i){i===void 0&&(i={});var e=i,t=e.defaultModifiers,n=t===void 0?[]:t,s=e.defaultOptions,o=s===void 0?Ri:s;return function(r,a,c){c===void 0&&(c=o);var h,d,u={placement:"bottom",orderedModifiers:[],options:Object.assign({},Ri,o),modifiersData:{},elements:{reference:r,popper:a},attributes:{},styles:{}},b=[],p=!1,g={state:u,setOptions:function(_){var C=typeof _=="function"?_(u.options):_;m(),u.options=Object.assign({},o,u.options,C),u.scrollParents={reference:be(r)?Xe(r):r.contextElement?Xe(r.contextElement):[],popper:Xe(a)};var k,y,A=function(v){var w=_s(v);return Ci.reduce(function(E,O){return E.concat(w.filter(function(T){return T.phase===O}))},[])}((k=[].concat(n,u.options.modifiers),y=k.reduce(function(v,w){var E=v[w.name];return v[w.name]=E?Object.assign({},E,w,{options:Object.assign({},E.options,w.options),data:Object.assign({},E.data,w.data)}):w,v},{}),Object.keys(y).map(function(v){return y[v]})));return u.orderedModifiers=A.filter(function(v){return v.enabled}),u.orderedModifiers.forEach(function(v){var w=v.name,E=v.options,O=E===void 0?{}:E,T=v.effect;if(typeof T=="function"){var L=T({state:u,name:w,instance:g,options:O});b.push(L||function(){})}}),g.update()},forceUpdate:function(){if(!p){var _=u.elements,C=_.reference,k=_.popper;if(Bi(C,k)){u.rects={reference:gs(C,Ke(k),u.options.strategy==="fixed"),popper:Lt(k)},u.reset=!1,u.placement=u.options.placement,u.orderedModifiers.forEach(function(T){return u.modifiersData[T.name]=Object.assign({},T.data)});for(var y=0;y<u.orderedModifiers.length;y++)if(u.reset!==!0){var A=u.orderedModifiers[y],v=A.fn,w=A.options,E=w===void 0?{}:w,O=A.name;typeof v=="function"&&(u=v({state:u,options:E,name:O,instance:g})||u)}else u.reset=!1,y=-1}}},update:(h=function(){return new Promise(function(_){g.forceUpdate(),_(u)})},function(){return d||(d=new Promise(function(_){Promise.resolve().then(function(){d=void 0,_(h())})})),d}),destroy:function(){m(),p=!0}};if(!Bi(r,a))return g;function m(){b.forEach(function(_){return _()}),b=[]}return g.setOptions(c).then(function(_){!p&&c.onFirstUpdate&&c.onFirstUpdate(_)}),g}}var bs=lt(),vs=lt({defaultModifiers:[It,Ft,Dt,kt]}),Ht=lt({defaultModifiers:[It,Ft,Dt,kt,qi,ji,Wi,$i,zi]});const Vi=Object.freeze(Object.defineProperty({__proto__:null,afterMain:wi,afterRead:bi,afterWrite:Ti,applyStyles:kt,arrow:$i,auto:nt,basePlacements:ke,beforeMain:vi,beforeRead:gi,beforeWrite:Ai,bottom:F,clippingParents:pi,computeStyles:Dt,createPopper:Ht,createPopperBase:bs,createPopperLite:vs,detectOverflow:Pe,end:Se,eventListeners:It,flip:ji,hide:zi,left:P,main:yi,modifierPhases:Ci,offset:qi,placements:Ot,popper:Le,popperGenerator:lt,popperOffsets:Ft,preventOverflow:Wi,read:_i,reference:mi,right:H,start:_e,top:I,variationPlacements:Ct,viewport:Tt,write:Ei},Symbol.toStringTag,{value:"Module"})),ue=new Map,zt={set(i,e,t){ue.has(i)||ue.set(i,new Map);const n=ue.get(i);n.has(e)||n.size===0?n.set(e,t):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(n.keys())[0]}.`)},get:(i,e)=>ue.has(i)&&ue.get(i).get(e)||null,remove(i,e){if(!ue.has(i))return;const t=ue.get(i);t.delete(e),t.size===0&&ue.delete(i)}},qt="transitionend",Ui=i=>(i&&window.CSS&&window.CSS.escape&&(i=i.replace(/#([^\s"#']+)/g,(e,t)=>`#${CSS.escape(t)}`)),i),ys=i=>i==null?`${i}`:Object.prototype.toString.call(i).match(/\s([a-z]+)/i)[1].toLowerCase(),Ki=i=>{i.dispatchEvent(new Event(qt))},se=i=>!(!i||typeof i!="object")&&(i.jquery!==void 0&&(i=i[0]),i.nodeType!==void 0),he=i=>se(i)?i.jquery?i[0]:i:typeof i=="string"&&i.length>0?document.querySelector(Ui(i)):null,Ne=i=>{if(!se(i)||i.getClientRects().length===0)return!1;const e=getComputedStyle(i).getPropertyValue("visibility")==="visible",t=i.closest("details:not([open])");if(!t)return e;if(t!==i){const n=i.closest("summary");if(n&&n.parentNode!==t||n===null)return!1}return e},de=i=>!i||i.nodeType!==Node.ELEMENT_NODE||!!i.classList.contains("disabled")||(i.disabled!==void 0?i.disabled:i.hasAttribute("disabled")&&i.getAttribute("disabled")!=="false"),Qi=i=>{if(!document.documentElement.attachShadow)return null;if(typeof i.getRootNode=="function"){const e=i.getRootNode();return e instanceof ShadowRoot?e:null}return i instanceof ShadowRoot?i:i.parentNode?Qi(i.parentNode):null},ct=()=>{},Ye=i=>{i.offsetHeight},Xi=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,Wt=[],B=()=>document.documentElement.dir==="rtl",V=i=>{var e;e=()=>{const t=Xi();if(t){const n=i.NAME,s=t.fn[n];t.fn[n]=i.jQueryInterface,t.fn[n].Constructor=i,t.fn[n].noConflict=()=>(t.fn[n]=s,i.jQueryInterface)}},document.readyState==="loading"?(Wt.length||document.addEventListener("DOMContentLoaded",()=>{for(const t of Wt)t()}),Wt.push(e)):e()},j=(i,e=[],t=i)=>typeof i=="function"?i.call(...e):t,Yi=(i,e,t=!0)=>{if(!t)return void j(i);const n=(r=>{if(!r)return 0;let{transitionDuration:a,transitionDelay:c}=window.getComputedStyle(r);const h=Number.parseFloat(a),d=Number.parseFloat(c);return h||d?(a=a.split(",")[0],c=c.split(",")[0],1e3*(Number.parseFloat(a)+Number.parseFloat(c))):0})(e)+5;let s=!1;const o=({target:r})=>{r===e&&(s=!0,e.removeEventListener(qt,o),j(i))};e.addEventListener(qt,o),setTimeout(()=>{s||Ki(e)},n)},Rt=(i,e,t,n)=>{const s=i.length;let o=i.indexOf(e);return o===-1?!t&&n?i[s-1]:i[0]:(o+=t?1:-1,n&&(o=(o+s)%s),i[Math.max(0,Math.min(o,s-1))])},ws=/[^.]*(?=\..*)\.|.*/,As=/\..*/,Es=/::\d+$/,Bt={};/*!
	  * Bootstrap v5.3.7 (https://getbootstrap.com/)
	  * Copyright 2011-2025 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
	  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
	  */let Ji=1;const Gi={mouseenter:"mouseover",mouseleave:"mouseout"},Ts=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function Zi(i,e){return e&&`${e}::${Ji++}`||i.uidEvent||Ji++}function en(i){const e=Zi(i);return i.uidEvent=e,Bt[e]=Bt[e]||{},Bt[e]}function tn(i,e,t=null){return Object.values(i).find(n=>n.callable===e&&n.delegationSelector===t)}function nn(i,e,t){const n=typeof e=="string",s=n?t:e||t;let o=on(i);return Ts.has(o)||(o=i),[n,s,o]}function sn(i,e,t,n,s){if(typeof e!="string"||!i)return;let[o,r,a]=nn(e,t,n);e in Gi&&(r=(g=>function(m){if(!m.relatedTarget||m.relatedTarget!==m.delegateTarget&&!m.delegateTarget.contains(m.relatedTarget))return g.call(this,m)})(r));const c=en(i),h=c[a]||(c[a]={}),d=tn(h,r,o?t:null);if(d)return void(d.oneOff=d.oneOff&&s);const u=Zi(r,e.replace(ws,"")),b=o?function(p,g,m){return function _(C){const k=p.querySelectorAll(g);for(let{target:y}=C;y&&y!==this;y=y.parentNode)for(const A of k)if(A===y)return Ut(C,{delegateTarget:y}),_.oneOff&&l.off(p,C.type,g,m),m.apply(y,[C])}}(i,t,r):function(p,g){return function m(_){return Ut(_,{delegateTarget:p}),m.oneOff&&l.off(p,_.type,g),g.apply(p,[_])}}(i,r);b.delegationSelector=o?t:null,b.callable=r,b.oneOff=s,b.uidEvent=u,h[u]=b,i.addEventListener(a,b,o)}function Vt(i,e,t,n,s){const o=tn(e[t],n,s);o&&(i.removeEventListener(t,o,!!s),delete e[t][o.uidEvent])}function Cs(i,e,t,n){const s=e[t]||{};for(const[o,r]of Object.entries(s))o.includes(n)&&Vt(i,e,t,r.callable,r.delegationSelector)}function on(i){return i=i.replace(As,""),Gi[i]||i}const l={on(i,e,t,n){sn(i,e,t,n,!1)},one(i,e,t,n){sn(i,e,t,n,!0)},off(i,e,t,n){if(typeof e!="string"||!i)return;const[s,o,r]=nn(e,t,n),a=r!==e,c=en(i),h=c[r]||{},d=e.startsWith(".");if(o===void 0){if(d)for(const u of Object.keys(c))Cs(i,c,u,e.slice(1));for(const[u,b]of Object.entries(h)){const p=u.replace(Es,"");a&&!e.includes(p)||Vt(i,c,r,b.callable,b.delegationSelector)}}else{if(!Object.keys(h).length)return;Vt(i,c,r,o,s?t:null)}},trigger(i,e,t){if(typeof e!="string"||!i)return null;const n=Xi();let s=null,o=!0,r=!0,a=!1;e!==on(e)&&n&&(s=n.Event(e,t),n(i).trigger(s),o=!s.isPropagationStopped(),r=!s.isImmediatePropagationStopped(),a=s.isDefaultPrevented());const c=Ut(new Event(e,{bubbles:o,cancelable:!0}),t);return a&&c.preventDefault(),r&&i.dispatchEvent(c),c.defaultPrevented&&s&&s.preventDefault(),c}};function Ut(i,e={}){for(const[t,n]of Object.entries(e))try{i[t]=n}catch{Object.defineProperty(i,t,{configurable:!0,get:()=>n})}return i}function rn(i){if(i==="true")return!0;if(i==="false")return!1;if(i===Number(i).toString())return Number(i);if(i===""||i==="null")return null;if(typeof i!="string")return i;try{return JSON.parse(decodeURIComponent(i))}catch{return i}}function Kt(i){return i.replace(/[A-Z]/g,e=>`-${e.toLowerCase()}`)}const oe={setDataAttribute(i,e,t){i.setAttribute(`data-bs-${Kt(e)}`,t)},removeDataAttribute(i,e){i.removeAttribute(`data-bs-${Kt(e)}`)},getDataAttributes(i){if(!i)return{};const e={},t=Object.keys(i.dataset).filter(n=>n.startsWith("bs")&&!n.startsWith("bsConfig"));for(const n of t){let s=n.replace(/^bs/,"");s=s.charAt(0).toLowerCase()+s.slice(1),e[s]=rn(i.dataset[n])}return e},getDataAttribute:(i,e)=>rn(i.getAttribute(`data-bs-${Kt(e)}`))};class Je{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e}_mergeConfigObj(e,t){const n=se(t)?oe.getDataAttribute(t,"config"):{};return{...this.constructor.Default,...typeof n=="object"?n:{},...se(t)?oe.getDataAttributes(t):{},...typeof e=="object"?e:{}}}_typeCheckConfig(e,t=this.constructor.DefaultType){for(const[n,s]of Object.entries(t)){const o=e[n],r=se(o)?"element":ys(o);if(!new RegExp(s).test(r))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${n}" provided type "${r}" but expected type "${s}".`)}}}class Y extends Je{constructor(e,t){super(),(e=he(e))&&(this._element=e,this._config=this._getConfig(t),zt.set(this._element,this.constructor.DATA_KEY,this))}dispose(){zt.remove(this._element,this.constructor.DATA_KEY),l.off(this._element,this.constructor.EVENT_KEY);for(const e of Object.getOwnPropertyNames(this))this[e]=null}_queueCallback(e,t,n=!0){Yi(e,t,n)}_getConfig(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}static getInstance(e){return zt.get(he(e),this.DATA_KEY)}static getOrCreateInstance(e,t={}){return this.getInstance(e)||new this(e,typeof t=="object"?t:null)}static get VERSION(){return"5.3.7"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(e){return`${e}${this.EVENT_KEY}`}}const Qt=i=>{let e=i.getAttribute("data-bs-target");if(!e||e==="#"){let t=i.getAttribute("href");if(!t||!t.includes("#")&&!t.startsWith("."))return null;t.includes("#")&&!t.startsWith("#")&&(t=`#${t.split("#")[1]}`),e=t&&t!=="#"?t.trim():null}return e?e.split(",").map(t=>Ui(t)).join(","):null},f={find:(i,e=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(e,i)),findOne:(i,e=document.documentElement)=>Element.prototype.querySelector.call(e,i),children:(i,e)=>[].concat(...i.children).filter(t=>t.matches(e)),parents(i,e){const t=[];let n=i.parentNode.closest(e);for(;n;)t.push(n),n=n.parentNode.closest(e);return t},prev(i,e){let t=i.previousElementSibling;for(;t;){if(t.matches(e))return[t];t=t.previousElementSibling}return[]},next(i,e){let t=i.nextElementSibling;for(;t;){if(t.matches(e))return[t];t=t.nextElementSibling}return[]},focusableChildren(i){const e=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(t=>`${t}:not([tabindex^="-"])`).join(",");return this.find(e,i).filter(t=>!de(t)&&Ne(t))},getSelectorFromElement(i){const e=Qt(i);return e&&f.findOne(e)?e:null},getElementFromSelector(i){const e=Qt(i);return e?f.findOne(e):null},getMultipleElementsFromSelector(i){const e=Qt(i);return e?f.find(e):[]}},ut=(i,e="hide")=>{const t=`click.dismiss${i.EVENT_KEY}`,n=i.NAME;l.on(document,t,`[data-bs-dismiss="${n}"]`,function(s){if(["A","AREA"].includes(this.tagName)&&s.preventDefault(),de(this))return;const o=f.getElementFromSelector(this)||this.closest(`.${n}`);i.getOrCreateInstance(o)[e]()})},an=".bs.alert",Os=`close${an}`,xs=`closed${an}`;class Me extends Y{static get NAME(){return"alert"}close(){if(l.trigger(this._element,Os).defaultPrevented)return;this._element.classList.remove("show");const e=this._element.classList.contains("fade");this._queueCallback(()=>this._destroyElement(),this._element,e)}_destroyElement(){this._element.remove(),l.trigger(this._element,xs),this.dispose()}static jQueryInterface(e){return this.each(function(){const t=Me.getOrCreateInstance(this);if(typeof e=="string"){if(t[e]===void 0||e.startsWith("_")||e==="constructor")throw new TypeError(`No method named "${e}"`);t[e](this)}})}}ut(Me,"close"),V(Me);const ln='[data-bs-toggle="button"]';class je extends Y{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(e){return this.each(function(){const t=je.getOrCreateInstance(this);e==="toggle"&&t[e]()})}}l.on(document,"click.bs.button.data-api",ln,i=>{i.preventDefault();const e=i.target.closest(ln);je.getOrCreateInstance(e).toggle()}),V(je);const Fe=".bs.swipe",ks=`touchstart${Fe}`,Ss=`touchmove${Fe}`,Ls=`touchend${Fe}`,$s=`pointerdown${Fe}`,Ds=`pointerup${Fe}`,Is={endCallback:null,leftCallback:null,rightCallback:null},Ps={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class ht extends Je{constructor(e,t){super(),this._element=e,e&&ht.isSupported()&&(this._config=this._getConfig(t),this._deltaX=0,this._supportPointerEvents=!!window.PointerEvent,this._initEvents())}static get Default(){return Is}static get DefaultType(){return Ps}static get NAME(){return"swipe"}dispose(){l.off(this._element,Fe)}_start(e){this._supportPointerEvents?this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX):this._deltaX=e.touches[0].clientX}_end(e){this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX-this._deltaX),this._handleSwipe(),j(this._config.endCallback)}_move(e){this._deltaX=e.touches&&e.touches.length>1?0:e.touches[0].clientX-this._deltaX}_handleSwipe(){const e=Math.abs(this._deltaX);if(e<=40)return;const t=e/this._deltaX;this._deltaX=0,t&&j(t>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(l.on(this._element,$s,e=>this._start(e)),l.on(this._element,Ds,e=>this._end(e)),this._element.classList.add("pointer-event")):(l.on(this._element,ks,e=>this._start(e)),l.on(this._element,Ss,e=>this._move(e)),l.on(this._element,Ls,e=>this._end(e)))}_eventIsPointerPenTouch(e){return this._supportPointerEvents&&(e.pointerType==="pen"||e.pointerType==="touch")}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const fe=".bs.carousel",cn=".data-api",Ns="ArrowLeft",Ms="ArrowRight",Ge="next",He="prev",ze="left",dt="right",js=`slide${fe}`,Xt=`slid${fe}`,Fs=`keydown${fe}`,Hs=`mouseenter${fe}`,zs=`mouseleave${fe}`,qs=`dragstart${fe}`,Ws=`load${fe}${cn}`,Rs=`click${fe}${cn}`,un="carousel",ft="active",hn=".active",dn=".carousel-item",Bs=hn+dn,Vs={[Ns]:dt,[Ms]:ze},Us={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},Ks={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class ye extends Y{constructor(e,t){super(e,t),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=f.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===un&&this.cycle()}static get Default(){return Us}static get DefaultType(){return Ks}static get NAME(){return"carousel"}next(){this._slide(Ge)}nextWhenVisible(){!document.hidden&&Ne(this._element)&&this.next()}prev(){this._slide(He)}pause(){this._isSliding&&Ki(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?l.one(this._element,Xt,()=>this.cycle()):this.cycle())}to(e){const t=this._getItems();if(e>t.length-1||e<0)return;if(this._isSliding)return void l.one(this._element,Xt,()=>this.to(e));const n=this._getItemIndex(this._getActive());if(n===e)return;const s=e>n?Ge:He;this._slide(s,t[e])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(e){return e.defaultInterval=e.interval,e}_addEventListeners(){this._config.keyboard&&l.on(this._element,Fs,e=>this._keydown(e)),this._config.pause==="hover"&&(l.on(this._element,Hs,()=>this.pause()),l.on(this._element,zs,()=>this._maybeEnableCycle())),this._config.touch&&ht.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const t of f.find(".carousel-item img",this._element))l.on(t,qs,n=>n.preventDefault());const e={leftCallback:()=>this._slide(this._directionToOrder(ze)),rightCallback:()=>this._slide(this._directionToOrder(dt)),endCallback:()=>{this._config.pause==="hover"&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),500+this._config.interval))}};this._swipeHelper=new ht(this._element,e)}_keydown(e){if(/input|textarea/i.test(e.target.tagName))return;const t=Vs[e.key];t&&(e.preventDefault(),this._slide(this._directionToOrder(t)))}_getItemIndex(e){return this._getItems().indexOf(e)}_setActiveIndicatorElement(e){if(!this._indicatorsElement)return;const t=f.findOne(hn,this._indicatorsElement);t.classList.remove(ft),t.removeAttribute("aria-current");const n=f.findOne(`[data-bs-slide-to="${e}"]`,this._indicatorsElement);n&&(n.classList.add(ft),n.setAttribute("aria-current","true"))}_updateInterval(){const e=this._activeElement||this._getActive();if(!e)return;const t=Number.parseInt(e.getAttribute("data-bs-interval"),10);this._config.interval=t||this._config.defaultInterval}_slide(e,t=null){if(this._isSliding)return;const n=this._getActive(),s=e===Ge,o=t||Rt(this._getItems(),n,s,this._config.wrap);if(o===n)return;const r=this._getItemIndex(o),a=u=>l.trigger(this._element,u,{relatedTarget:o,direction:this._orderToDirection(e),from:this._getItemIndex(n),to:r});if(a(js).defaultPrevented||!n||!o)return;const c=!!this._interval;this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(r),this._activeElement=o;const h=s?"carousel-item-start":"carousel-item-end",d=s?"carousel-item-next":"carousel-item-prev";o.classList.add(d),Ye(o),n.classList.add(h),o.classList.add(h),this._queueCallback(()=>{o.classList.remove(h,d),o.classList.add(ft),n.classList.remove(ft,d,h),this._isSliding=!1,a(Xt)},n,this._isAnimated()),c&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return f.findOne(Bs,this._element)}_getItems(){return f.find(dn,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(e){return B()?e===ze?He:Ge:e===ze?Ge:He}_orderToDirection(e){return B()?e===He?ze:dt:e===He?dt:ze}static jQueryInterface(e){return this.each(function(){const t=ye.getOrCreateInstance(this,e);if(typeof e!="number"){if(typeof e=="string"){if(t[e]===void 0||e.startsWith("_")||e==="constructor")throw new TypeError(`No method named "${e}"`);t[e]()}}else t.to(e)})}}l.on(document,Rs,"[data-bs-slide], [data-bs-slide-to]",function(i){const e=f.getElementFromSelector(this);if(!e||!e.classList.contains(un))return;i.preventDefault();const t=ye.getOrCreateInstance(e),n=this.getAttribute("data-bs-slide-to");return n?(t.to(n),void t._maybeEnableCycle()):oe.getDataAttribute(this,"slide")==="next"?(t.next(),void t._maybeEnableCycle()):(t.prev(),void t._maybeEnableCycle())}),l.on(window,Ws,()=>{const i=f.find('[data-bs-ride="carousel"]');for(const e of i)ye.getOrCreateInstance(e)}),V(ye);const Ze=".bs.collapse",Qs=`show${Ze}`,Xs=`shown${Ze}`,Ys=`hide${Ze}`,Js=`hidden${Ze}`,Gs=`click${Ze}.data-api`,Yt="show",qe="collapse",pt="collapsing",Zs=`:scope .${qe} .${qe}`,Jt='[data-bs-toggle="collapse"]',eo={parent:null,toggle:!0},to={parent:"(null|element)",toggle:"boolean"};class we extends Y{constructor(e,t){super(e,t),this._isTransitioning=!1,this._triggerArray=[];const n=f.find(Jt);for(const s of n){const o=f.getSelectorFromElement(s),r=f.find(o).filter(a=>a===this._element);o!==null&&r.length&&this._triggerArray.push(s)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return eo}static get DefaultType(){return to}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let e=[];if(this._config.parent&&(e=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter(s=>s!==this._element).map(s=>we.getOrCreateInstance(s,{toggle:!1}))),e.length&&e[0]._isTransitioning||l.trigger(this._element,Qs).defaultPrevented)return;for(const s of e)s.hide();const t=this._getDimension();this._element.classList.remove(qe),this._element.classList.add(pt),this._element.style[t]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const n=`scroll${t[0].toUpperCase()+t.slice(1)}`;this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(pt),this._element.classList.add(qe,Yt),this._element.style[t]="",l.trigger(this._element,Xs)},this._element,!0),this._element.style[t]=`${this._element[n]}px`}hide(){if(this._isTransitioning||!this._isShown()||l.trigger(this._element,Ys).defaultPrevented)return;const e=this._getDimension();this._element.style[e]=`${this._element.getBoundingClientRect()[e]}px`,Ye(this._element),this._element.classList.add(pt),this._element.classList.remove(qe,Yt);for(const t of this._triggerArray){const n=f.getElementFromSelector(t);n&&!this._isShown(n)&&this._addAriaAndCollapsedClass([t],!1)}this._isTransitioning=!0,this._element.style[e]="",this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(pt),this._element.classList.add(qe),l.trigger(this._element,Js)},this._element,!0)}_isShown(e=this._element){return e.classList.contains(Yt)}_configAfterMerge(e){return e.toggle=!!e.toggle,e.parent=he(e.parent),e}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;const e=this._getFirstLevelChildren(Jt);for(const t of e){const n=f.getElementFromSelector(t);n&&this._addAriaAndCollapsedClass([t],this._isShown(n))}}_getFirstLevelChildren(e){const t=f.find(Zs,this._config.parent);return f.find(e,this._config.parent).filter(n=>!t.includes(n))}_addAriaAndCollapsedClass(e,t){if(e.length)for(const n of e)n.classList.toggle("collapsed",!t),n.setAttribute("aria-expanded",t)}static jQueryInterface(e){const t={};return typeof e=="string"&&/show|hide/.test(e)&&(t.toggle=!1),this.each(function(){const n=we.getOrCreateInstance(this,t);if(typeof e=="string"){if(n[e]===void 0)throw new TypeError(`No method named "${e}"`);n[e]()}})}}l.on(document,Gs,Jt,function(i){(i.target.tagName==="A"||i.delegateTarget&&i.delegateTarget.tagName==="A")&&i.preventDefault();for(const e of f.getMultipleElementsFromSelector(this))we.getOrCreateInstance(e,{toggle:!1}).toggle()}),V(we);const fn="dropdown",Ae=".bs.dropdown",Gt=".data-api",io="ArrowUp",pn="ArrowDown",no=`hide${Ae}`,so=`hidden${Ae}`,oo=`show${Ae}`,ro=`shown${Ae}`,mn=`click${Ae}${Gt}`,gn=`keydown${Ae}${Gt}`,ao=`keyup${Ae}${Gt}`,We="show",Ee='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',lo=`${Ee}.${We}`,mt=".dropdown-menu",co=B()?"top-end":"top-start",uo=B()?"top-start":"top-end",ho=B()?"bottom-end":"bottom-start",fo=B()?"bottom-start":"bottom-end",po=B()?"left-start":"right-start",mo=B()?"right-start":"left-start",go={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},_o={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class q extends Y{constructor(e,t){super(e,t),this._popper=null,this._parent=this._element.parentNode,this._menu=f.next(this._element,mt)[0]||f.prev(this._element,mt)[0]||f.findOne(mt,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return go}static get DefaultType(){return _o}static get NAME(){return fn}toggle(){return this._isShown()?this.hide():this.show()}show(){if(de(this._element)||this._isShown())return;const e={relatedTarget:this._element};if(!l.trigger(this._element,oo,e).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const t of[].concat(...document.body.children))l.on(t,"mouseover",ct);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(We),this._element.classList.add(We),l.trigger(this._element,ro,e)}}hide(){if(de(this._element)||!this._isShown())return;const e={relatedTarget:this._element};this._completeHide(e)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(e){if(!l.trigger(this._element,no,e).defaultPrevented){if("ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))l.off(t,"mouseover",ct);this._popper&&this._popper.destroy(),this._menu.classList.remove(We),this._element.classList.remove(We),this._element.setAttribute("aria-expanded","false"),oe.removeDataAttribute(this._menu,"popper"),l.trigger(this._element,so,e),this._element.focus()}}_getConfig(e){if(typeof(e=super._getConfig(e)).reference=="object"&&!se(e.reference)&&typeof e.reference.getBoundingClientRect!="function")throw new TypeError(`${fn.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return e}_createPopper(){if(Vi===void 0)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org/docs/v2/)");let e=this._element;this._config.reference==="parent"?e=this._parent:se(this._config.reference)?e=he(this._config.reference):typeof this._config.reference=="object"&&(e=this._config.reference);const t=this._getPopperConfig();this._popper=Ht(e,this._menu,t)}_isShown(){return this._menu.classList.contains(We)}_getPlacement(){const e=this._parent;if(e.classList.contains("dropend"))return po;if(e.classList.contains("dropstart"))return mo;if(e.classList.contains("dropup-center"))return"top";if(e.classList.contains("dropdown-center"))return"bottom";const t=getComputedStyle(this._menu).getPropertyValue("--bs-position").trim()==="end";return e.classList.contains("dropup")?t?uo:co:t?fo:ho}_detectNavbar(){return this._element.closest(".navbar")!==null}_getOffset(){const{offset:e}=this._config;return typeof e=="string"?e.split(",").map(t=>Number.parseInt(t,10)):typeof e=="function"?t=>e(t,this._element):e}_getPopperConfig(){const e={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||this._config.display==="static")&&(oe.setDataAttribute(this._menu,"popper","static"),e.modifiers=[{name:"applyStyles",enabled:!1}]),{...e,...j(this._config.popperConfig,[void 0,e])}}_selectMenuItem({key:e,target:t}){const n=f.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter(s=>Ne(s));n.length&&Rt(n,t,e===pn,!n.includes(t)).focus()}static jQueryInterface(e){return this.each(function(){const t=q.getOrCreateInstance(this,e);if(typeof e=="string"){if(t[e]===void 0)throw new TypeError(`No method named "${e}"`);t[e]()}})}static clearMenus(e){if(e.button===2||e.type==="keyup"&&e.key!=="Tab")return;const t=f.find(lo);for(const n of t){const s=q.getInstance(n);if(!s||s._config.autoClose===!1)continue;const o=e.composedPath(),r=o.includes(s._menu);if(o.includes(s._element)||s._config.autoClose==="inside"&&!r||s._config.autoClose==="outside"&&r||s._menu.contains(e.target)&&(e.type==="keyup"&&e.key==="Tab"||/input|select|option|textarea|form/i.test(e.target.tagName)))continue;const a={relatedTarget:s._element};e.type==="click"&&(a.clickEvent=e),s._completeHide(a)}}static dataApiKeydownHandler(e){const t=/input|textarea/i.test(e.target.tagName),n=e.key==="Escape",s=[io,pn].includes(e.key);if(!s&&!n||t&&!n)return;e.preventDefault();const o=this.matches(Ee)?this:f.prev(this,Ee)[0]||f.next(this,Ee)[0]||f.findOne(Ee,e.delegateTarget.parentNode),r=q.getOrCreateInstance(o);if(s)return e.stopPropagation(),r.show(),void r._selectMenuItem(e);r._isShown()&&(e.stopPropagation(),r.hide(),o.focus())}}l.on(document,gn,Ee,q.dataApiKeydownHandler),l.on(document,gn,mt,q.dataApiKeydownHandler),l.on(document,mn,q.clearMenus),l.on(document,ao,q.clearMenus),l.on(document,mn,Ee,function(i){i.preventDefault(),q.getOrCreateInstance(this).toggle()}),V(q);const _n="backdrop",bn="show",vn=`mousedown.bs.${_n}`,bo={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},vo={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class yn extends Je{constructor(e){super(),this._config=this._getConfig(e),this._isAppended=!1,this._element=null}static get Default(){return bo}static get DefaultType(){return vo}static get NAME(){return _n}show(e){if(!this._config.isVisible)return void j(e);this._append();const t=this._getElement();this._config.isAnimated&&Ye(t),t.classList.add(bn),this._emulateAnimation(()=>{j(e)})}hide(e){this._config.isVisible?(this._getElement().classList.remove(bn),this._emulateAnimation(()=>{this.dispose(),j(e)})):j(e)}dispose(){this._isAppended&&(l.off(this._element,vn),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const e=document.createElement("div");e.className=this._config.className,this._config.isAnimated&&e.classList.add("fade"),this._element=e}return this._element}_configAfterMerge(e){return e.rootElement=he(e.rootElement),e}_append(){if(this._isAppended)return;const e=this._getElement();this._config.rootElement.append(e),l.on(e,vn,()=>{j(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(e){Yi(e,this._getElement(),this._config.isAnimated)}}const gt=".bs.focustrap",yo=`focusin${gt}`,wo=`keydown.tab${gt}`,wn="backward",Ao={autofocus:!0,trapElement:null},Eo={autofocus:"boolean",trapElement:"element"};class An extends Je{constructor(e){super(),this._config=this._getConfig(e),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return Ao}static get DefaultType(){return Eo}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),l.off(document,gt),l.on(document,yo,e=>this._handleFocusin(e)),l.on(document,wo,e=>this._handleKeydown(e)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,l.off(document,gt))}_handleFocusin(e){const{trapElement:t}=this._config;if(e.target===document||e.target===t||t.contains(e.target))return;const n=f.focusableChildren(t);n.length===0?t.focus():this._lastTabNavDirection===wn?n[n.length-1].focus():n[0].focus()}_handleKeydown(e){e.key==="Tab"&&(this._lastTabNavDirection=e.shiftKey?wn:"forward")}}const En=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Tn=".sticky-top",_t="padding-right",Cn="margin-right";class Zt{constructor(){this._element=document.body}getWidth(){const e=document.documentElement.clientWidth;return Math.abs(window.innerWidth-e)}hide(){const e=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,_t,t=>t+e),this._setElementAttributes(En,_t,t=>t+e),this._setElementAttributes(Tn,Cn,t=>t-e)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,_t),this._resetElementAttributes(En,_t),this._resetElementAttributes(Tn,Cn)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(e,t,n){const s=this.getWidth();this._applyManipulationCallback(e,o=>{if(o!==this._element&&window.innerWidth>o.clientWidth+s)return;this._saveInitialAttribute(o,t);const r=window.getComputedStyle(o).getPropertyValue(t);o.style.setProperty(t,`${n(Number.parseFloat(r))}px`)})}_saveInitialAttribute(e,t){const n=e.style.getPropertyValue(t);n&&oe.setDataAttribute(e,t,n)}_resetElementAttributes(e,t){this._applyManipulationCallback(e,n=>{const s=oe.getDataAttribute(n,t);s!==null?(oe.removeDataAttribute(n,t),n.style.setProperty(t,s)):n.style.removeProperty(t)})}_applyManipulationCallback(e,t){if(se(e))t(e);else for(const n of f.find(e,this._element))t(n)}}const U=".bs.modal",To=`hide${U}`,Co=`hidePrevented${U}`,On=`hidden${U}`,xn=`show${U}`,Oo=`shown${U}`,xo=`resize${U}`,ko=`click.dismiss${U}`,So=`mousedown.dismiss${U}`,Lo=`keydown.dismiss${U}`,$o=`click${U}.data-api`,kn="modal-open",Sn="show",ei="modal-static",Do={backdrop:!0,focus:!0,keyboard:!0},Io={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class pe extends Y{constructor(e,t){super(e,t),this._dialog=f.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new Zt,this._addEventListeners()}static get Default(){return Do}static get DefaultType(){return Io}static get NAME(){return"modal"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||this._isTransitioning||l.trigger(this._element,xn,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(kn),this._adjustDialog(),this._backdrop.show(()=>this._showElement(e)))}hide(){this._isShown&&!this._isTransitioning&&(l.trigger(this._element,To).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(Sn),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated())))}dispose(){l.off(window,U),l.off(this._dialog,U),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new yn({isVisible:!!this._config.backdrop,isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new An({trapElement:this._element})}_showElement(e){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const t=f.findOne(".modal-body",this._dialog);t&&(t.scrollTop=0),Ye(this._element),this._element.classList.add(Sn),this._queueCallback(()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,l.trigger(this._element,Oo,{relatedTarget:e})},this._dialog,this._isAnimated())}_addEventListeners(){l.on(this._element,Lo,e=>{e.key==="Escape"&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())}),l.on(window,xo,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),l.on(this._element,So,e=>{l.one(this._element,ko,t=>{this._element===e.target&&this._element===t.target&&(this._config.backdrop!=="static"?this._config.backdrop&&this.hide():this._triggerBackdropTransition())})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(kn),this._resetAdjustments(),this._scrollBar.reset(),l.trigger(this._element,On)})}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(l.trigger(this._element,Co).defaultPrevented)return;const e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._element.style.overflowY;t==="hidden"||this._element.classList.contains(ei)||(e||(this._element.style.overflowY="hidden"),this._element.classList.add(ei),this._queueCallback(()=>{this._element.classList.remove(ei),this._queueCallback(()=>{this._element.style.overflowY=t},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){const e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._scrollBar.getWidth(),n=t>0;if(n&&!e){const s=B()?"paddingLeft":"paddingRight";this._element.style[s]=`${t}px`}if(!n&&e){const s=B()?"paddingRight":"paddingLeft";this._element.style[s]=`${t}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(e,t){return this.each(function(){const n=pe.getOrCreateInstance(this,e);if(typeof e=="string"){if(n[e]===void 0)throw new TypeError(`No method named "${e}"`);n[e](t)}})}}l.on(document,$o,'[data-bs-toggle="modal"]',function(i){const e=f.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&i.preventDefault(),l.one(e,xn,n=>{n.defaultPrevented||l.one(e,On,()=>{Ne(this)&&this.focus()})});const t=f.findOne(".modal.show");t&&pe.getInstance(t).hide(),pe.getOrCreateInstance(e).toggle(this)}),ut(pe),V(pe);const re=".bs.offcanvas",Ln=".data-api",Po=`load${re}${Ln}`,$n="show",Dn="showing",In="hiding",Pn=".offcanvas.show",No=`show${re}`,Mo=`shown${re}`,jo=`hide${re}`,Nn=`hidePrevented${re}`,Mn=`hidden${re}`,Fo=`resize${re}`,Ho=`click${re}${Ln}`,zo=`keydown.dismiss${re}`,qo={backdrop:!0,keyboard:!0,scroll:!1},Wo={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class ee extends Y{constructor(e,t){super(e,t),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return qo}static get DefaultType(){return Wo}static get NAME(){return"offcanvas"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||l.trigger(this._element,No,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||new Zt().hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(Dn),this._queueCallback(()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add($n),this._element.classList.remove(Dn),l.trigger(this._element,Mo,{relatedTarget:e})},this._element,!0))}hide(){this._isShown&&(l.trigger(this._element,jo).defaultPrevented||(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(In),this._backdrop.hide(),this._queueCallback(()=>{this._element.classList.remove($n,In),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||new Zt().reset(),l.trigger(this._element,Mn)},this._element,!0)))}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const e=!!this._config.backdrop;return new yn({className:"offcanvas-backdrop",isVisible:e,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:e?()=>{this._config.backdrop!=="static"?this.hide():l.trigger(this._element,Nn)}:null})}_initializeFocusTrap(){return new An({trapElement:this._element})}_addEventListeners(){l.on(this._element,zo,e=>{e.key==="Escape"&&(this._config.keyboard?this.hide():l.trigger(this._element,Nn))})}static jQueryInterface(e){return this.each(function(){const t=ee.getOrCreateInstance(this,e);if(typeof e=="string"){if(t[e]===void 0||e.startsWith("_")||e==="constructor")throw new TypeError(`No method named "${e}"`);t[e](this)}})}}l.on(document,Ho,'[data-bs-toggle="offcanvas"]',function(i){const e=f.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&i.preventDefault(),de(this))return;l.one(e,Mn,()=>{Ne(this)&&this.focus()});const t=f.findOne(Pn);t&&t!==e&&ee.getInstance(t).hide(),ee.getOrCreateInstance(e).toggle(this)}),l.on(window,Po,()=>{for(const i of f.find(Pn))ee.getOrCreateInstance(i).show()}),l.on(window,Fo,()=>{for(const i of f.find("[aria-modal][class*=show][class*=offcanvas-]"))getComputedStyle(i).position!=="fixed"&&ee.getOrCreateInstance(i).hide()}),ut(ee),V(ee);const jn={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},Ro=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),Bo=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,Vo=(i,e)=>{const t=i.nodeName.toLowerCase();return e.includes(t)?!Ro.has(t)||!!Bo.test(i.nodeValue):e.filter(n=>n instanceof RegExp).some(n=>n.test(t))},Uo={allowList:jn,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},Ko={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},Qo={entry:"(string|element|function|null)",selector:"(string|element)"};class Xo extends Je{constructor(e){super(),this._config=this._getConfig(e)}static get Default(){return Uo}static get DefaultType(){return Ko}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map(e=>this._resolvePossibleFunction(e)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(e){return this._checkContent(e),this._config.content={...this._config.content,...e},this}toHtml(){const e=document.createElement("div");e.innerHTML=this._maybeSanitize(this._config.template);for(const[s,o]of Object.entries(this._config.content))this._setContent(e,o,s);const t=e.children[0],n=this._resolvePossibleFunction(this._config.extraClass);return n&&t.classList.add(...n.split(" ")),t}_typeCheckConfig(e){super._typeCheckConfig(e),this._checkContent(e.content)}_checkContent(e){for(const[t,n]of Object.entries(e))super._typeCheckConfig({selector:t,entry:n},Qo)}_setContent(e,t,n){const s=f.findOne(n,e);s&&((t=this._resolvePossibleFunction(t))?se(t)?this._putElementInTemplate(he(t),s):this._config.html?s.innerHTML=this._maybeSanitize(t):s.textContent=t:s.remove())}_maybeSanitize(e){return this._config.sanitize?function(t,n,s){if(!t.length)return t;if(s&&typeof s=="function")return s(t);const o=new window.DOMParser().parseFromString(t,"text/html"),r=[].concat(...o.body.querySelectorAll("*"));for(const a of r){const c=a.nodeName.toLowerCase();if(!Object.keys(n).includes(c)){a.remove();continue}const h=[].concat(...a.attributes),d=[].concat(n["*"]||[],n[c]||[]);for(const u of h)Vo(u,d)||a.removeAttribute(u.nodeName)}return o.body.innerHTML}(e,this._config.allowList,this._config.sanitizeFn):e}_resolvePossibleFunction(e){return j(e,[void 0,this])}_putElementInTemplate(e,t){if(this._config.html)return t.innerHTML="",void t.append(e);t.textContent=e.textContent}}const Yo=new Set(["sanitize","allowList","sanitizeFn"]),ti="fade",bt="show",Jo=".tooltip-inner",Fn=".modal",Hn="hide.bs.modal",et="hover",ii="focus",ni="click",Go={AUTO:"auto",TOP:"top",RIGHT:B()?"left":"right",BOTTOM:"bottom",LEFT:B()?"right":"left"},Zo={allowList:jn,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},er={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class ae extends Y{constructor(e,t){if(Vi===void 0)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org/docs/v2/)");super(e,t),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return Zo}static get DefaultType(){return er}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),l.off(this._element.closest(Fn),Hn,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if(this._element.style.display==="none")throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const e=l.trigger(this._element,this.constructor.eventName("show")),t=(Qi(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(e.defaultPrevented||!t)return;this._disposePopper();const n=this._getTipElement();this._element.setAttribute("aria-describedby",n.getAttribute("id"));const{container:s}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(s.append(n),l.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(n),n.classList.add(bt),"ontouchstart"in document.documentElement)for(const o of[].concat(...document.body.children))l.on(o,"mouseover",ct);this._queueCallback(()=>{l.trigger(this._element,this.constructor.eventName("shown")),this._isHovered===!1&&this._leave(),this._isHovered=!1},this.tip,this._isAnimated())}hide(){if(this._isShown()&&!l.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented){if(this._getTipElement().classList.remove(bt),"ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))l.off(e,"mouseover",ct);this._activeTrigger[ni]=!1,this._activeTrigger[ii]=!1,this._activeTrigger[et]=!1,this._isHovered=null,this._queueCallback(()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),l.trigger(this._element,this.constructor.eventName("hidden")))},this.tip,this._isAnimated())}}update(){this._popper&&this._popper.update()}_isWithContent(){return!!this._getTitle()}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(e){const t=this._getTemplateFactory(e).toHtml();if(!t)return null;t.classList.remove(ti,bt),t.classList.add(`bs-${this.constructor.NAME}-auto`);const n=(s=>{do s+=Math.floor(1e6*Math.random());while(document.getElementById(s));return s})(this.constructor.NAME).toString();return t.setAttribute("id",n),this._isAnimated()&&t.classList.add(ti),t}setContent(e){this._newContent=e,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(e){return this._templateFactory?this._templateFactory.changeContent(e):this._templateFactory=new Xo({...this._config,content:e,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[Jo]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(e){return this.constructor.getOrCreateInstance(e.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(ti)}_isShown(){return this.tip&&this.tip.classList.contains(bt)}_createPopper(e){const t=j(this._config.placement,[this,e,this._element]),n=Go[t.toUpperCase()];return Ht(this._element,e,this._getPopperConfig(n))}_getOffset(){const{offset:e}=this._config;return typeof e=="string"?e.split(",").map(t=>Number.parseInt(t,10)):typeof e=="function"?t=>e(t,this._element):e}_resolvePossibleFunction(e){return j(e,[this._element,this._element])}_getPopperConfig(e){const t={placement:e,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:n=>{this._getTipElement().setAttribute("data-popper-placement",n.state.placement)}}]};return{...t,...j(this._config.popperConfig,[void 0,t])}}_setListeners(){const e=this._config.trigger.split(" ");for(const t of e)if(t==="click")l.on(this._element,this.constructor.eventName("click"),this._config.selector,n=>{const s=this._initializeOnDelegatedTarget(n);s._activeTrigger[ni]=!(s._isShown()&&s._activeTrigger[ni]),s.toggle()});else if(t!=="manual"){const n=t===et?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),s=t===et?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");l.on(this._element,n,this._config.selector,o=>{const r=this._initializeOnDelegatedTarget(o);r._activeTrigger[o.type==="focusin"?ii:et]=!0,r._enter()}),l.on(this._element,s,this._config.selector,o=>{const r=this._initializeOnDelegatedTarget(o);r._activeTrigger[o.type==="focusout"?ii:et]=r._element.contains(o.relatedTarget),r._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},l.on(this._element.closest(Fn),Hn,this._hideModalHandler)}_fixTitle(){const e=this._element.getAttribute("title");e&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",e),this._element.setAttribute("data-bs-original-title",e),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(e,t){clearTimeout(this._timeout),this._timeout=setTimeout(e,t)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(e){const t=oe.getDataAttributes(this._element);for(const n of Object.keys(t))Yo.has(n)&&delete t[n];return e={...t,...typeof e=="object"&&e?e:{}},e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e.container=e.container===!1?document.body:he(e.container),typeof e.delay=="number"&&(e.delay={show:e.delay,hide:e.delay}),typeof e.title=="number"&&(e.title=e.title.toString()),typeof e.content=="number"&&(e.content=e.content.toString()),e}_getDelegateConfig(){const e={};for(const[t,n]of Object.entries(this._config))this.constructor.Default[t]!==n&&(e[t]=n);return e.selector=!1,e.trigger="manual",e}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(e){return this.each(function(){const t=ae.getOrCreateInstance(this,e);if(typeof e=="string"){if(t[e]===void 0)throw new TypeError(`No method named "${e}"`);t[e]()}})}}V(ae);const tr=".popover-header",ir=".popover-body",nr={...ae.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},sr={...ae.DefaultType,content:"(null|string|element|function)"};class Re extends ae{static get Default(){return nr}static get DefaultType(){return sr}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[tr]:this._getTitle(),[ir]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(e){return this.each(function(){const t=Re.getOrCreateInstance(this,e);if(typeof e=="string"){if(t[e]===void 0)throw new TypeError(`No method named "${e}"`);t[e]()}})}}V(Re);const si=".bs.scrollspy",or=`activate${si}`,zn=`click${si}`,rr=`load${si}.data-api`,Be="active",oi="[href]",qn=".nav-link",ar=`${qn}, .nav-item > ${qn}, .list-group-item`,lr={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},cr={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class Ve extends Y{constructor(e,t){super(e,t),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement=getComputedStyle(this._element).overflowY==="visible"?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return lr}static get DefaultType(){return cr}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const e of this._observableSections.values())this._observer.observe(e)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(e){return e.target=he(e.target)||document.body,e.rootMargin=e.offset?`${e.offset}px 0px -30%`:e.rootMargin,typeof e.threshold=="string"&&(e.threshold=e.threshold.split(",").map(t=>Number.parseFloat(t))),e}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(l.off(this._config.target,zn),l.on(this._config.target,zn,oi,e=>{const t=this._observableSections.get(e.target.hash);if(t){e.preventDefault();const n=this._rootElement||window,s=t.offsetTop-this._element.offsetTop;if(n.scrollTo)return void n.scrollTo({top:s,behavior:"smooth"});n.scrollTop=s}}))}_getNewObserver(){const e={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(t=>this._observerCallback(t),e)}_observerCallback(e){const t=r=>this._targetLinks.get(`#${r.target.id}`),n=r=>{this._previousScrollData.visibleEntryTop=r.target.offsetTop,this._process(t(r))},s=(this._rootElement||document.documentElement).scrollTop,o=s>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=s;for(const r of e){if(!r.isIntersecting){this._activeTarget=null,this._clearActiveClass(t(r));continue}const a=r.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(o&&a){if(n(r),!s)return}else o||a||n(r)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const e=f.find(oi,this._config.target);for(const t of e){if(!t.hash||de(t))continue;const n=f.findOne(decodeURI(t.hash),this._element);Ne(n)&&(this._targetLinks.set(decodeURI(t.hash),t),this._observableSections.set(t.hash,n))}}_process(e){this._activeTarget!==e&&(this._clearActiveClass(this._config.target),this._activeTarget=e,e.classList.add(Be),this._activateParents(e),l.trigger(this._element,or,{relatedTarget:e}))}_activateParents(e){if(e.classList.contains("dropdown-item"))f.findOne(".dropdown-toggle",e.closest(".dropdown")).classList.add(Be);else for(const t of f.parents(e,".nav, .list-group"))for(const n of f.prev(t,ar))n.classList.add(Be)}_clearActiveClass(e){e.classList.remove(Be);const t=f.find(`${oi}.${Be}`,e);for(const n of t)n.classList.remove(Be)}static jQueryInterface(e){return this.each(function(){const t=Ve.getOrCreateInstance(this,e);if(typeof e=="string"){if(t[e]===void 0||e.startsWith("_")||e==="constructor")throw new TypeError(`No method named "${e}"`);t[e]()}})}}l.on(window,rr,()=>{for(const i of f.find('[data-bs-spy="scroll"]'))Ve.getOrCreateInstance(i)}),V(Ve);const Te=".bs.tab",ur=`hide${Te}`,hr=`hidden${Te}`,dr=`show${Te}`,fr=`shown${Te}`,pr=`click${Te}`,mr=`keydown${Te}`,gr=`load${Te}`,_r="ArrowLeft",Wn="ArrowRight",br="ArrowUp",Rn="ArrowDown",ri="Home",Bn="End",Ce="active",Vn="fade",ai="show",Un=".dropdown-toggle",li=`:not(${Un})`,Kn='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',ci=`.nav-link${li}, .list-group-item${li}, [role="tab"]${li}, ${Kn}`,vr=`.${Ce}[data-bs-toggle="tab"], .${Ce}[data-bs-toggle="pill"], .${Ce}[data-bs-toggle="list"]`;class le extends Y{constructor(e){super(e),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),l.on(this._element,mr,t=>this._keydown(t)))}static get NAME(){return"tab"}show(){const e=this._element;if(this._elemIsActive(e))return;const t=this._getActiveElem(),n=t?l.trigger(t,ur,{relatedTarget:e}):null;l.trigger(e,dr,{relatedTarget:t}).defaultPrevented||n&&n.defaultPrevented||(this._deactivate(t,e),this._activate(e,t))}_activate(e,t){e&&(e.classList.add(Ce),this._activate(f.getElementFromSelector(e)),this._queueCallback(()=>{e.getAttribute("role")==="tab"?(e.removeAttribute("tabindex"),e.setAttribute("aria-selected",!0),this._toggleDropDown(e,!0),l.trigger(e,fr,{relatedTarget:t})):e.classList.add(ai)},e,e.classList.contains(Vn)))}_deactivate(e,t){e&&(e.classList.remove(Ce),e.blur(),this._deactivate(f.getElementFromSelector(e)),this._queueCallback(()=>{e.getAttribute("role")==="tab"?(e.setAttribute("aria-selected",!1),e.setAttribute("tabindex","-1"),this._toggleDropDown(e,!1),l.trigger(e,hr,{relatedTarget:t})):e.classList.remove(ai)},e,e.classList.contains(Vn)))}_keydown(e){if(![_r,Wn,br,Rn,ri,Bn].includes(e.key))return;e.stopPropagation(),e.preventDefault();const t=this._getChildren().filter(s=>!de(s));let n;if([ri,Bn].includes(e.key))n=t[e.key===ri?0:t.length-1];else{const s=[Wn,Rn].includes(e.key);n=Rt(t,e.target,s,!0)}n&&(n.focus({preventScroll:!0}),le.getOrCreateInstance(n).show())}_getChildren(){return f.find(ci,this._parent)}_getActiveElem(){return this._getChildren().find(e=>this._elemIsActive(e))||null}_setInitialAttributes(e,t){this._setAttributeIfNotExists(e,"role","tablist");for(const n of t)this._setInitialAttributesOnChild(n)}_setInitialAttributesOnChild(e){e=this._getInnerElement(e);const t=this._elemIsActive(e),n=this._getOuterElement(e);e.setAttribute("aria-selected",t),n!==e&&this._setAttributeIfNotExists(n,"role","presentation"),t||e.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(e,"role","tab"),this._setInitialAttributesOnTargetPanel(e)}_setInitialAttributesOnTargetPanel(e){const t=f.getElementFromSelector(e);t&&(this._setAttributeIfNotExists(t,"role","tabpanel"),e.id&&this._setAttributeIfNotExists(t,"aria-labelledby",`${e.id}`))}_toggleDropDown(e,t){const n=this._getOuterElement(e);if(!n.classList.contains("dropdown"))return;const s=(o,r)=>{const a=f.findOne(o,n);a&&a.classList.toggle(r,t)};s(Un,Ce),s(".dropdown-menu",ai),n.setAttribute("aria-expanded",t)}_setAttributeIfNotExists(e,t,n){e.hasAttribute(t)||e.setAttribute(t,n)}_elemIsActive(e){return e.classList.contains(Ce)}_getInnerElement(e){return e.matches(ci)?e:f.findOne(ci,e)}_getOuterElement(e){return e.closest(".nav-item, .list-group-item")||e}static jQueryInterface(e){return this.each(function(){const t=le.getOrCreateInstance(this);if(typeof e=="string"){if(t[e]===void 0||e.startsWith("_")||e==="constructor")throw new TypeError(`No method named "${e}"`);t[e]()}})}}l.on(document,pr,Kn,function(i){["A","AREA"].includes(this.tagName)&&i.preventDefault(),de(this)||le.getOrCreateInstance(this).show()}),l.on(window,gr,()=>{for(const i of f.find(vr))le.getOrCreateInstance(i)}),V(le);const me=".bs.toast",yr=`mouseover${me}`,wr=`mouseout${me}`,Ar=`focusin${me}`,Er=`focusout${me}`,Tr=`hide${me}`,Cr=`hidden${me}`,Or=`show${me}`,xr=`shown${me}`,Qn="hide",vt="show",yt="showing",kr={animation:"boolean",autohide:"boolean",delay:"number"},Sr={animation:!0,autohide:!0,delay:5e3};class Oe extends Y{constructor(e,t){super(e,t),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return Sr}static get DefaultType(){return kr}static get NAME(){return"toast"}show(){l.trigger(this._element,Or).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove(Qn),Ye(this._element),this._element.classList.add(vt,yt),this._queueCallback(()=>{this._element.classList.remove(yt),l.trigger(this._element,xr),this._maybeScheduleHide()},this._element,this._config.animation))}hide(){this.isShown()&&(l.trigger(this._element,Tr).defaultPrevented||(this._element.classList.add(yt),this._queueCallback(()=>{this._element.classList.add(Qn),this._element.classList.remove(yt,vt),l.trigger(this._element,Cr)},this._element,this._config.animation)))}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(vt),super.dispose()}isShown(){return this._element.classList.contains(vt)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(e,t){switch(e.type){case"mouseover":case"mouseout":this._hasMouseInteraction=t;break;case"focusin":case"focusout":this._hasKeyboardInteraction=t}if(t)return void this._clearTimeout();const n=e.relatedTarget;this._element===n||this._element.contains(n)||this._maybeScheduleHide()}_setListeners(){l.on(this._element,yr,e=>this._onInteraction(e,!0)),l.on(this._element,wr,e=>this._onInteraction(e,!1)),l.on(this._element,Ar,e=>this._onInteraction(e,!0)),l.on(this._element,Er,e=>this._onInteraction(e,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(e){return this.each(function(){const t=Oe.getOrCreateInstance(this,e);if(typeof e=="string"){if(t[e]===void 0)throw new TypeError(`No method named "${e}"`);t[e](this)}})}}ut(Oe),V(Oe);const Lr=Object.freeze(Object.defineProperty({__proto__:null,Alert:Me,Button:je,Carousel:ye,Collapse:we,Dropdown:q,Modal:pe,Offcanvas:ee,Popover:Re,ScrollSpy:Ve,Tab:le,Toast:Oe,Tooltip:ae},Symbol.toStringTag,{value:"Module"}));[].slice.call(document.querySelectorAll('[data-bs-toggle="dropdown"]')).map(function(i){let e={boundary:i.getAttribute("data-bs-boundary")==="viewport"?document.querySelector(".btn"):"clippingParents"};return new q(i,e)}),[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map(function(i){let e={delay:{show:50,hide:50},html:i.getAttribute("data-bs-html")==="true",placement:i.getAttribute("data-bs-placement")??"auto"};return new ae(i,e)}),[].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]')).map(function(i){let e={delay:{show:50,hide:50},html:i.getAttribute("data-bs-html")==="true",placement:i.getAttribute("data-bs-placement")??"auto"};return new Re(i,e)}),[].slice.call(document.querySelectorAll('[data-bs-toggle="switch-icon"]')).map(function(i){i.addEventListener("click",e=>{e.stopPropagation(),i.classList.toggle("active")})}),(()=>{const i=window.location.hash;i&&[].slice.call(document.querySelectorAll('[data-bs-toggle="tab"]')).filter(e=>e.hash===i).map(e=>{new le(e).show()})})(),[].slice.call(document.querySelectorAll('[data-bs-toggle="toast"]')).map(function(i){if(!i.hasAttribute("data-bs-target"))return;const e=new Oe(i.getAttribute("data-bs-target"));i.addEventListener("click",()=>{e.show()})});const Xn="tblr-",Yn=(i,e)=>{const t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(i);return t?`rgba(${parseInt(t[1],16)}, ${parseInt(t[2],16)}, ${parseInt(t[3],16)}, ${e})`:null},$r=Object.freeze(Object.defineProperty({__proto__:null,getColor:(i,e=1)=>{const t=getComputedStyle(document.body).getPropertyValue(`--${Xn}${i}`).trim();return e!==1?Yn(t,e):t},hexToRgba:Yn,prefix:Xn},Symbol.toStringTag,{value:"Module"}));D.Alert=Me,D.Button=je,D.Carousel=ye,D.Collapse=we,D.Dropdown=q,D.Modal=pe,D.Offcanvas=ee,D.Popover=Re,D.ScrollSpy=Ve,D.Tab=le,D.Toast=Oe,D.Tooltip=ae,D.bootstrap=Lr,D.tabler=$r,Object.defineProperty(D,Symbol.toStringTag,{value:"Module"})})}(tt,tt.exports)),tt.exports}qr();
