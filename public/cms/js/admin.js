// Toast instance sẽ được khởi tạo khi DOM ready
let toastInstance = null;

// Khởi tạo toast khi DOM ready
$(document).ready(function() {
    const toastElement = document.getElementById('toast-admin');
    if (toastElement && typeof bootstrap !== 'undefined') {
        toastInstance = new bootstrap.Toast(toastElement, {
            autohide: true,
            delay: 5000
        });
    }
});

function showNotify(content, type = 'info') {
    // Fallback nếu bootstrap chưa sẵn sàng
    if (!toastInstance || typeof bootstrap === 'undefined') {
        console.warn('Bootstrap Toast not available, falling back to alert');
        alert(content);
        return;
    }

    let classBg = 'bg-info';
    switch (type) {
        case 'error':
            classBg = 'bg-danger';
            break;
        case 'success':
            classBg = 'bg-success';
            break;
        case 'warning':
            classBg = 'bg-warning';
            break;
        case 'info':
        default:
            classBg = 'bg-info';
            break;
    }

    const $toast = $('#toast-admin');
    $toast.removeClass('bg-info bg-success bg-danger bg-warning').addClass(classBg);
    $toast.find('.toast-body').html(content);
    toastInstance.show();
}
