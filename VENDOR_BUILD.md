# Vendor Library Build System

This project uses Vite to build and manage vendor libraries (Bootstrap, jQuery, and Tabler) into the `public/vendor/<name>` directories.

## Available Scripts

### Build All Libraries
```bash
npm run build
```
Builds all vendor libraries (Tabler, Bootstrap, and jQuery) to their respective directories.

### Build Individual Libraries
```bash
npm run build:tabler      # Build Tabler only
npm run build:bootstrap   # Build Bootstrap only  
npm run build:jquery      # Build jQuery only
```

### Clean and Build
```bash
npm run build:clean              # Clean all and build all
npm run build:clean:tabler       # Clean and build Tabler only
npm run build:clean:bootstrap    # Clean and build Bootstrap only
npm run build:clean:jquery       # Clean and build jQuery only
```

### Clean Only
```bash
npm run clean              # Clean all vendor directories
npm run clean:tabler       # Clean Tabler directory only
npm run clean:bootstrap    # Clean Bootstrap directory only
npm run clean:jquery       # Clean jQuery directory only
```

## Output Structure

After building, the vendor libraries will be organized as follows:

```
public/vendor/
├── bootstrap/
│   ├── bootstrap.min.css
│   ├── bootstrap.min.css.map
│   └── bootstrap.min.js
├── jquery/
│   └── jquery.min.js
└── tabler/
    ├── img/
    ├── tabler.min.css
    ├── tabler-flags.min.css
    ├── tabler-payments.min.css
    ├── tabler-socials.min.css
    ├── tabler-themes.min.css
    └── tabler.min.js
```

## Configuration

The build system is configured in `vite.config.js` and uses environment variables to determine which library to build:

- `TARGET_LIB=tabler` - Builds Tabler
- `TARGET_LIB=bootstrap` - Builds Bootstrap  
- `TARGET_LIB=jquery` - Builds jQuery

## Adding New Libraries

To add a new vendor library:

1. Install the library via npm: `npm install <library-name>`
2. Add configuration to the `libConfigs` object in `vite.config.js`
3. Add CSS/asset configurations if needed
4. Add new build scripts to `package.json`

## Development Workflow

1. Install dependencies: `npm install`
2. Build vendor libraries: `npm run build`
3. The built files will be available in `public/vendor/<library-name>/`
4. Include the files in your HTML/templates as needed

## Notes

- The build process automatically handles CSS, JS, and asset files for each library
- Source maps are included where available
- The build output is optimized and minified
- Each library is built independently to avoid conflicts
