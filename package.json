{"private": true, "type": "module", "scripts": {"build": "npm run build:tabler && npm run build:bootstrap && npm run build:j<PERSON>y", "build:tabler": "TARGET_LIB=tabler vite build", "build:bootstrap": "TARGET_LIB=bootstrap vite build", "build:jquery": "TARGET_LIB=jquery vite build", "clean": "rm -rf public/vendor/tabler/* public/vendor/bootstrap/* public/vendor/jquery/*", "clean:tabler": "rm -rf public/vendor/tabler/*", "clean:bootstrap": "rm -rf public/vendor/bootstrap/*", "clean:jquery": "rm -rf public/vendor/jquery/*", "build:clean": "npm run clean && npm run build", "build:clean:tabler": "npm run clean:tabler && npm run build:tabler", "build:clean:bootstrap": "npm run clean:bootstrap && npm run build:bootstrap", "build:clean:jquery": "npm run clean:jquery && npm run build:jquery"}, "devDependencies": {"vite": "^7.0.6"}, "dependencies": {"@tabler/core": "^1.4.0", "bootstrap": "5.3.7", "jquery": "^3.7.1"}}