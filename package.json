{"private": true, "type": "module", "scripts": {"build": "npm run build:tabler && npm run build:j<PERSON>y", "build:tabler": "TARGET_LIB=tabler vite build", "build:jquery": "TARGET_LIB=jquery vite build", "clean": "rm -rf public/vendor/tabler/* public/vendor/jquery/*", "clean:tabler": "rm -rf public/vendor/tabler/*", "clean:jquery": "rm -rf public/vendor/jquery/*", "build:clean": "npm run clean && npm run build", "build:clean:tabler": "npm run clean:tabler && npm run build:tabler", "build:clean:jquery": "npm run clean:jquery && npm run build:jquery"}, "devDependencies": {"vite": "^7.0.6"}, "dependencies": {"@tabler/core": "^1.4.0", "jquery": "^3.7.1"}}