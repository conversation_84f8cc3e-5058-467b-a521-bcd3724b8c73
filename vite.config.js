import { defineConfig } from 'vite';
import { readFileSync, existsSync, mkdirSync, readdirSync, copyFileSync } from 'fs';
import { resolve, join } from 'path';

// Get the target library from environment variable or default to 'tabler'
const TARGET_LIB = process.env.TARGET_LIB || 'tabler';

// Configuration for each library
const libConfigs = {
    tabler: {
        outDir: 'public/vendor/tabler',
        input: {
            'tabler.min': resolve('node_modules/@tabler/core/dist/js/tabler.min.js')
        }
    },
    bootstrap: {
        outDir: 'public/vendor/bootstrap',
        input: {
            'bootstrap.min': resolve('node_modules/bootstrap/dist/js/bootstrap.min.js')
        }
    },
    jquery: {
        outDir: 'public/vendor/jquery',
        input: {
            'jquery.min': resolve('node_modules/jquery/dist/jquery.min.js'),
            'jquery': resolve('node_modules/jquery/dist/jquery.js')
        }
    }
};

const currentConfig = libConfigs[TARGET_LIB];

export default defineConfig({
    publicDir: false,
    build: {
        outDir: currentConfig.outDir,
        emptyOutDir: true,
        rollupOptions: {
            input: currentConfig.input,
            output: {
                assetFileNames: '[name].[ext]',
                entryFileNames: '[name].js'
            }
        }
    },
    plugins: [
        {
            name: 'copy-library-assets',
            generateBundle() {
                // CSS files configuration for each library
                const cssConfigs = {
                    tabler: {
                        basePath: 'node_modules/@tabler/core/dist',
                        files: [
                            { src: 'css/tabler.min.css', dest: 'tabler.min.css' },
                            { src: 'css/tabler-flags.min.css', dest: 'tabler-flags.min.css' },
                            { src: 'css/tabler-payments.min.css', dest: 'tabler-payments.min.css' },
                            { src: 'css/tabler-socials.min.css', dest: 'tabler-socials.min.css' },
                            { src: 'css/tabler-themes.min.css', dest: 'tabler-themes.min.css' }
                        ]
                    },
                    bootstrap: {
                        basePath: 'node_modules/bootstrap/dist',
                        files: [
                            { src: 'css/bootstrap.min.css', dest: 'bootstrap.min.css' },
                            { src: 'css/bootstrap.min.css.map', dest: 'bootstrap.min.css.map' }
                        ]
                    },
                    jquery: {
                        basePath: 'node_modules/jquery/dist',
                        files: [
                            // jQuery doesn't have CSS files, but we keep the structure consistent
                        ]
                    }
                };

                const config = cssConfigs[TARGET_LIB];
                if (config && config.files.length > 0) {
                    config.files.forEach(({ src, dest }) => {
                        try {
                            this.emitFile({
                                type: 'asset',
                                fileName: dest,
                                source: readFileSync(resolve(`${config.basePath}/${src}`))
                            });
                        } catch (e) {
                            console.log(`File ${src} not found, skipping...`);
                        }
                    });
                }
            }
        },
        {
            name: 'copy-library-assets',
            writeBundle() {
                const copyDir = (src, dest) => {
                    if (!existsSync(src)) return;

                    if (!existsSync(dest)) {
                        mkdirSync(dest, { recursive: true });
                    }

                    const entries = readdirSync(src, { withFileTypes: true });

                    for (let entry of entries) {
                        const srcPath = join(src, entry.name);
                        const destPath = join(dest, entry.name);

                        if (entry.isDirectory()) {
                            copyDir(srcPath, destPath);
                        } else {
                            copyFileSync(srcPath, destPath);
                        }
                    }
                };

                // Asset directories configuration for each library
                const assetConfigs = {
                    tabler: [
                        {
                            src: resolve('node_modules/@tabler/core/dist/img'),
                            dest: resolve(`public/vendor/tabler/img`)
                        }
                    ],
                    bootstrap: [
                        // Bootstrap doesn't have additional assets like images
                    ],
                    jquery: [
                        // jQuery doesn't have additional assets like images
                    ]
                };

                const config = assetConfigs[TARGET_LIB];
                if (config) {
                    config.forEach(({ src, dest }) => {
                        copyDir(src, dest);
                    });
                }
            }
        }
    ]
});
