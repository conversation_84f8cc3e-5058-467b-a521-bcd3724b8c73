import { defineConfig } from 'vite';
import { readFileSync, existsSync, mkdirSync, readdirSync, copyFileSync } from 'fs';
import { resolve, join } from 'path';

export default defineConfig({
    publicDir: false,
    build: {
        outDir: 'public/vendor/tabler',
        emptyOutDir: true,
        rollupOptions: {
            input: {
                'tabler.min': resolve('node_modules/@tabler/core/dist/js/tabler.min.js')
            },
            output: {
                assetFileNames: '[name].[ext]',
                entryFileNames: '[name].js'
            }
        }
    },
    plugins: [
        {
            name: 'copy-tabler-css',
            generateBundle() {
                const tablerPath = 'node_modules/@tabler/core/dist';

                // Copy only CSS files (JS is handled by input)
                const cssFiles = [
                    { src: 'css/tabler.min.css', dest: 'tabler.min.css' },
                    { src: 'css/tabler-flags.min.css', dest: 'tabler-flags.min.css' },
                    { src: 'css/tabler-payments.min.css', dest: 'tabler-payments.min.css' },
                    { src: 'css/tabler-socials.min.css', dest: 'tabler-socials.min.css' },
                    { src: 'css/tabler-themes.min.css', dest: 'tabler-themes.min.css' }
                ];

                cssFiles.forEach(({ src, dest }) => {
                    try {
                        this.emitFile({
                            type: 'asset',
                            fileName: dest,
                            source: readFileSync(resolve(`${tablerPath}/${src}`))
                        });
                    } catch (e) {
                        console.log(`File ${src} not found, skipping...`);
                    }
                });
            }
        },
        {
            name: 'copy-tabler-images',
            writeBundle() {
                const copyDir = (src, dest) => {
                    if (!existsSync(src)) return;

                    if (!existsSync(dest)) {
                        mkdirSync(dest, { recursive: true });
                    }

                    const entries = readdirSync(src, { withFileTypes: true });

                    for (let entry of entries) {
                        const srcPath = join(src, entry.name);
                        const destPath = join(dest, entry.name);

                        if (entry.isDirectory()) {
                            copyDir(srcPath, destPath);
                        } else {
                            copyFileSync(srcPath, destPath);
                        }
                    }
                };

                const imgSrc = resolve('node_modules/@tabler/core/dist/img');
                const imgDest = resolve('public/vendor/tabler/img');
                copyDir(imgSrc, imgDest);
            }
        }
    ]
});
