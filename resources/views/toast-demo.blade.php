@extends('core/base::layouts.master')
@section('page_title', 'Bootstrap Toast Demo')

@section('content')
<div class="page-header d-print-none">
    <div class="container-xl">
        <div class="row g-2 align-items-center">
            <div class="col">
                <h2 class="page-title">Bootstrap Toast Demo</h2>
            </div>
        </div>
    </div>
</div>

<div class="page-body">
    <div class="container-xl">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Test Bootstrap Toast</h3>
                    </div>
                    <div class="card-body">
                        <p>Click the buttons below to test different types of toast notifications:</p>
                        
                        <div class="btn-list">
                            <button type="button" class="btn btn-info" onclick="showNotify('This is an info message!', 'info')">
                                Show Info Toast
                            </button>
                            <button type="button" class="btn btn-success" onclick="showNotify('Operation completed successfully!', 'success')">
                                Show Success Toast
                            </button>
                            <button type="button" class="btn btn-warning" onclick="showNotify('This is a warning message!', 'warning')">
                                Show Warning Toast
                            </button>
                            <button type="button" class="btn btn-danger" onclick="showNotify('An error occurred!', 'error')">
                                Show Error Toast
                            </button>
                        </div>

                        <hr>

                        <h4>Manual Bootstrap Toast Example</h4>
                        <p>You can also create toasts manually using Bootstrap's Toast API:</p>
                        
                        <button type="button" class="btn btn-primary" onclick="createManualToast()">
                            Create Manual Toast
                        </button>

                        <!-- Manual toast container -->
                        <div class="position-fixed bottom-0 start-0 p-3" style="z-index: 1001">
                            <div id="manual-toast" class="toast align-items-center text-white bg-primary border-0" role="alert" aria-live="assertive" aria-atomic="true">
                                <div class="d-flex">
                                    <div class="toast-body">
                                        This is a manually created toast!
                                    </div>
                                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function createManualToast() {
    // Create a new Bootstrap Toast instance
    const manualToastElement = document.getElementById('manual-toast');
    const manualToast = new bootstrap.Toast(manualToastElement, {
        autohide: true,
        delay: 3000
    });
    
    // Show the toast
    manualToast.show();
}

// Example of creating dynamic toasts
function createDynamicToast(message, type = 'primary') {
    // Create toast HTML
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;
    
    // Create container if it doesn't exist
    let container = document.getElementById('dynamic-toast-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'dynamic-toast-container';
        container.className = 'position-fixed top-0 end-0 p-3';
        container.style.zIndex = '1002';
        document.body.appendChild(container);
    }
    
    // Add toast to container
    container.insertAdjacentHTML('beforeend', toastHtml);
    
    // Get the newly added toast and show it
    const newToast = container.lastElementChild;
    const toast = new bootstrap.Toast(newToast);
    toast.show();
    
    // Remove toast element after it's hidden
    newToast.addEventListener('hidden.bs.toast', function() {
        newToast.remove();
    });
}
</script>
@endpush
