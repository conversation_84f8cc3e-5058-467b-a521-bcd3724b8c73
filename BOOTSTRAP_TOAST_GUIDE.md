# Bootstrap Toast trong Tabler - H<PERSON>ớng dẫn sử dụng

## Tổng quan

Hệ thống đã được cấu hình để sử dụng Bootstrap Toast trong ứng dụng Tabler. Bootstrap Toast cung cấp thông báo nhẹ và không gây gián đoạn cho người dùng.

## Cấu hình đã thực hiện

### 1. Vendor Libraries
- Bootstrap JavaScript đã được build và đặt tại `public/vendor/bootstrap/bootstrap.min.js`
- jQuery đã có sẵn tại `public/vendor/jquery/jquery.min.js`

### 2. Scripts được include
Trong `cms/core/base/resources/views/layouts/partials/scripts.blade.php`:
```html
<script src="{{ asset('vendor/bootstrap/bootstrap.min.js') }}"></script>
<script src="{{ asset('vendor/tabler/tabler.min.js') }}"></script>
<script src="{{ asset('cms/js/admin.js') }}"></script>
```

### 3. HTML Toast Container
```html
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1000">
    <div id="toast-admin" class="toast align-items-center text-white bg-info border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body"></div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>
```

## Cách sử dụng

### 1. Sử dụng hàm showNotify() có sẵn

```javascript
// Hiển thị thông báo info (mặc định)
showNotify('Đây là thông báo thông tin!');

// Hiển thị thông báo thành công
showNotify('Thao tác thành công!', 'success');

// Hiển thị thông báo cảnh báo
showNotify('Đây là cảnh báo!', 'warning');

// Hiển thị thông báo lỗi
showNotify('Có lỗi xảy ra!', 'error');
```

### 2. Tạo Toast thủ công với Bootstrap API

```javascript
// Tạo Toast instance
const toastElement = document.getElementById('toast-admin');
const toast = new bootstrap.Toast(toastElement, {
    autohide: true,
    delay: 5000 // Tự động ẩn sau 5 giây
});

// Hiển thị toast
toast.show();
```

### 3. Tạo Toast động

```javascript
function createDynamicToast(message, type = 'primary') {
    // Tạo HTML cho toast
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;
    
    // Tạo container nếu chưa có
    let container = document.getElementById('dynamic-toast-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'dynamic-toast-container';
        container.className = 'position-fixed top-0 end-0 p-3';
        container.style.zIndex = '1002';
        document.body.appendChild(container);
    }
    
    // Thêm toast vào container
    container.insertAdjacentHTML('beforeend', toastHtml);
    
    // Lấy toast vừa thêm và hiển thị
    const newToast = container.lastElementChild;
    const toast = new bootstrap.Toast(newToast);
    toast.show();
    
    // Xóa toast sau khi ẩn
    newToast.addEventListener('hidden.bs.toast', function() {
        newToast.remove();
    });
}
```

## Các loại Toast có sẵn

- `info` - Thông báo thông tin (bg-info)
- `success` - Thông báo thành công (bg-success)
- `warning` - Thông báo cảnh báo (bg-warning)
- `error` - Thông báo lỗi (bg-danger)

## Tùy chọn cấu hình Toast

```javascript
const toast = new bootstrap.Toast(element, {
    animation: true,    // Bật/tắt animation
    autohide: true,     // Tự động ẩn
    delay: 5000        // Thời gian delay (ms)
});
```

## Demo và Test

Truy cập `/admin/toast-demo` để xem demo và test các tính năng Toast.

## Sử dụng trong Laravel

### Trong Controller:
```php
// Thông báo thành công
session()->flash('flash_data', [
    'message' => 'Thao tác thành công!',
    'type' => 'success'
]);

// Thông báo lỗi
session()->flash('flash_data', [
    'message' => 'Có lỗi xảy ra!',
    'type' => 'error'
]);
```

### Trong Blade template:
```blade
@if (session('flash_data'))
    @php
        $flash_data = session('flash_data');
    @endphp
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            showNotify("{{ $flash_data['message'] }}", "{{ $flash_data['type'] }}");
        });
    </script>
@endif
```

## Lưu ý

1. Bootstrap JavaScript phải được load trước khi sử dụng Toast
2. Toast container phải có trong DOM trước khi khởi tạo
3. Sử dụng z-index phù hợp để Toast hiển thị trên các element khác
4. Toast sẽ tự động ẩn sau thời gian delay được cấu hình
